<?php
echo "<!DOCTYPE html><html><head><title>NCHM Attendance - Network Setup</title></head><body>";
echo "<h1>🌐 NCHM Attendance System - Network Setup</h1>";

// Check current configuration
echo "<h2>📊 Current Configuration</h2>";
echo "<strong>Server IP:</strong> " . ($_SERVER['SERVER_ADDR'] ?? 'Not detected') . "<br>";
echo "<strong>Access URL:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'localhost') . "<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Current Directory:</strong> " . __DIR__ . "<br>";

// Test database connection
echo "<h2>🗄️ Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "✅ Database connection successful<br>";
        echo "✅ Connected to: " . $db->getHost() . "<br>";
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Network accessibility test
echo "<h2>🔗 Network Access Test</h2>";
echo "<p>Test these URLs from other devices on your network:</p>";
echo "<ul>";
echo "<li><strong>Localhost:</strong> <a href='http://localhost:8080/attendance/'>http://localhost:8080/attendance/</a></li>";
echo "<li><strong>Network IP:</strong> <a href='http://*************:8080/attendance/'>http://*************:8080/attendance/</a></li>";
echo "</ul>";

// Configuration steps
echo "<h2>⚙️ Laragon Configuration Steps</h2>";
echo "<ol>";
echo "<li><strong>Apache Configuration:</strong>";
echo "<ul>";
echo "<li>Open Laragon → Apache → httpd.conf</li>";
echo "<li>Find 'Listen 80' and change to 'Listen 0.0.0.0:80'</li>";
echo "<li>Restart Apache</li>";
echo "</ul></li>";

echo "<li><strong>Windows Firewall:</strong>";
echo "<ul>";
echo "<li>Windows Firewall → Advanced Settings</li>";
echo "<li>Inbound Rules → New Rule → Port → TCP → 80</li>";
echo "<li>Allow connection → All profiles</li>";
echo "</ul></li>";

echo "<li><strong>Network Test:</strong>";
echo "<ul>";
echo "<li>From another device: ping *************</li>";
echo "<li>Open browser: http://*************/attendance/</li>";
echo "</ul></li>";
echo "</ol>";

// Quick links
echo "<h2>🔗 Quick Links</h2>";
echo "<a href='index.php'>Main Page</a> | ";
echo "<a href='admin/simple_login.php'>Admin Login</a> | ";
echo "<a href='network_test.php'>Network Test</a> | ";
echo "<a href='info.php'>System Info</a>";

echo "</body></html>";
?>
