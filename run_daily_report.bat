@echo off
REM Daily Attendance Report Email Sender
REM This batch file runs the PHP script to send daily attendance reports
REM Scheduled to run daily at 8:00 PM via Windows Task Scheduler

REM Set the working directory to the attendance system folder
cd /d "C:\laragon\www\attendance"

REM Log the execution
echo %date% %time% - Starting daily report sender >> logs\scheduler.log

REM Run the PHP script using Laragon's PHP
"C:\laragon\bin\php\php-8.1.10-Win32-vs16-x64\php.exe" daily_report_sender.php

REM Log completion
echo %date% %time% - Daily report sender completed >> logs\scheduler.log

REM Keep window open for 5 seconds if run manually (for debugging)
timeout /t 5 /nobreak > nul
