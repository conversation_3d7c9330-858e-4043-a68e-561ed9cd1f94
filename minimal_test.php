<!DOCTYPE html>
<html>
<head>
    <title>Minimal Test</title>
</head>
<body>
    <h1>DHM Attendance System - Minimal Test</h1>
    
    <?php
    echo "<h2>PHP Status</h2>";
    echo "<p>PHP Version: " . PHP_VERSION . "</p>";
    echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
    echo "<p>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
    echo "<p>Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";
    
    echo "<h2>Extensions</h2>";
    echo "<p>PDO: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "</p>";
    echo "<p>PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "</p>";
    echo "<p>GD: " . (extension_loaded('gd') ? 'Yes' : 'No') . "</p>";
    echo "<p>JSON: " . (extension_loaded('json') ? 'Yes' : 'No') . "</p>";
    
    echo "<h2>Database Test</h2>";
    try {
        $pdo = new PDO("mysql:host=localhost", "root", "");
        echo "<p>✅ MySQL Connection: Success</p>";
        
        $stmt = $pdo->query("SHOW DATABASES LIKE 'attendance_db'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Database 'attendance_db': Found</p>";
        } else {
            echo "<p>⚠️ Database 'attendance_db': Not found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ MySQL Connection: Failed - " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>File System</h2>";
    echo "<p>Current Directory: " . __DIR__ . "</p>";
    echo "<p>Writable: " . (is_writable(__DIR__) ? 'Yes' : 'No') . "</p>";
    
    $dirs = ['qr_codes', 'logs', 'admin'];
    foreach ($dirs as $dir) {
        if (is_dir($dir)) {
            echo "<p>Directory '$dir': Exists</p>";
        } else {
            echo "<p>Directory '$dir': Missing</p>";
        }
    }
    
    echo "<h2>Quick Links</h2>";
    echo "<p><a href='debug.php'>Debug Test</a></p>";
    echo "<p><a href='info.php'>PHP Info</a></p>";
    echo "<p><a href='admin/simple_login.php'>Admin Login</a></p>";
    ?>
</body>
</html>
