<?php
// NCHM Attendance System - Main Page
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session for admin check
session_start();

// Check if user is super admin
$is_super_admin = isset($_SESSION['admin_logged_in']) &&
                  $_SESSION['admin_logged_in'] &&
                  $_SESSION['admin_role'] === 'super_admin';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCHM Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }

        .header-section {
            background: white;
            border-radius: 20px 20px 0 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            padding: 20px 40px;
            max-width: 600px;
            width: 100%;
            margin: 0 auto 0 auto;
            text-align: center;
            border-bottom: 3px solid #3498db;
        }

        .official-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .logo-left, .logo-right {
            width: 80px;
            height: 80px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 50%;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .logo-left img, .logo-right img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 50%;
        }

        .logo-fallback {
            display: none;
            text-align: center;
            line-height: 1.2;
        }

        .header-text {
            flex: 1;
            margin: 0 20px;
        }

        .dzongkha-text {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .english-text {
            font-size: 18px;
            color: #34495e;
            font-weight: 600;
            line-height: 1.3;
        }

        .main-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 40px);
        }

        .container {
            background: white;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
            margin: 0 auto;
        }

        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            font-weight: bold;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 32px;
        }

        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 18px;
        }

        .qr-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .qr-title {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .qr-code-img {
            max-width: 300px;
            width: 100%;
            border: 3px solid #3498db;
            border-radius: 15px;
            margin: 20px 0;
        }

        .qr-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: left;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #e1e8ed;
        }

        .info-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .info-value {
            color: #7f8c8d;
        }

        .actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-admin {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .btn-admin:hover {
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .qr-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .action-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 18px 25px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            min-width: 180px;
            flex: 1;
            max-width: 220px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .register-btn {
            background: linear-gradient(45deg, #e67e22, #d35400);
        }

        .register-btn:hover {
            box-shadow: 0 8px 25px rgba(230, 126, 34, 0.4);
        }

        .attendance-btn {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .attendance-btn:hover {
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        }

        .admin-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .admin-btn:hover {
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .instructions {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: left;
        }

        .instructions h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .instructions ol {
            color: #34495e;
            line-height: 1.6;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .footer {
            margin-top: 40px;
            color: #7f8c8d;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .header-section {
                padding: 15px 20px;
                border-radius: 15px 15px 0 0;
            }

            .official-header {
                flex-direction: column;
                gap: 15px;
            }

            .logo-left, .logo-right {
                width: 60px;
                height: 60px;
            }

            .header-text {
                margin: 0;
            }

            .dzongkha-text {
                font-size: 18px;
            }

            .english-text {
                font-size: 14px;
            }

            .container {
                padding: 20px;
                border-radius: 0 0 15px 15px;
            }

            .actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }

            .qr-actions {
                flex-direction: column;
                gap: 15px;
                align-items: center;
            }

            .action-btn {
                min-width: 250px;
                max-width: 300px;
                padding: 20px 30px;
                font-size: 18px;
                flex: none;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Official NCHM Header -->
        <div class="header-section">
            <div class="official-header">
                <div class="logo-left">
                    <img src="assets/bhutan-emblem.png" alt="Royal Government of Bhutan" onerror="this.style.display='none'; this.parentNode.querySelector('.logo-fallback').style.display='block';">
                    <div class="logo-fallback">RGoB</div>
                </div>
                <div class="header-text">
                    <div class="dzongkha-text">རྒྱལ་ཡོངས་ཆུ་དྭད་དང་གནམ་གཤིས་རིག་པའི་ལྟེ་བ།</div>
                    <div class="english-text">National Center for Hydrology and Meteorology<br>Royal Government of Bhutan</div>
                </div>
                <div class="logo-right">
                    <img src="assets/nchm-logo.png" alt="NCHM Logo" onerror="this.style.display='none'; this.parentNode.querySelector('.logo-fallback').style.display='block';">
                    <div class="logo-fallback">NCHM</div>
                </div>
            </div>
        </div>

        <!-- Main Content Container -->
        <div class="container">
            <div class="logo">NCHM</div>
            <h1>NCHM Attendance System</h1>
            <p class="subtitle">Staff Attendance Management System</p>

        <!-- QR Code Actions -->
        <div class="qr-actions">
            <button id="registerBtn" class="action-btn register-btn">
                📱 Scan for Register
            </button>
            <button id="attendanceBtn" class="action-btn attendance-btn">
                ✅ Scan for Attendance
            </button>
            <button id="adminBtn" class="action-btn admin-btn" onclick="window.location.href='admin/simple_login.php'">
                🔐 Admin Login
            </button>
        </div>

        <!-- QR Code Display Section -->
        <div id="qrSection" class="qr-section" style="display: none;">
            <div id="qrContent">
                <!-- QR code will be loaded here -->
            </div>
        </div>



        <div class="instructions">
            <h3>How to Use:</h3>
            <ol>
                <li><strong>First Time:</strong> Get your unique registration QR code from admin and scan it to register your device</li>
                <li><strong>Daily Use:</strong> Scan the daily QR code above with your registered device</li>
                <li><strong>Clock In/Out:</strong> The system automatically determines if you're clocking in or out</li>
                <li><strong>Security:</strong> Only registered devices can record attendance</li>
                <li><strong>Network:</strong> Must be connected to office Wi-Fi (172.31.18.x)</li>
            </ol>
        </div>

        <div class="footer">
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_NAME'] . ':' . $_SERVER['SERVER_PORT']; ?></p>
            <p><strong>Last Updated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>© <?php echo date('Y'); ?> National Center for Hydrology and Meteorology, Royal Government of Bhutan</p>
        </div>
    </div>
    </div>

    <script>
        // QR Code functionality
        document.addEventListener('DOMContentLoaded', function() {
            const registerBtn = document.getElementById('registerBtn');
            const attendanceBtn = document.getElementById('attendanceBtn');
            const qrSection = document.getElementById('qrSection');
            const qrContent = document.getElementById('qrContent');

            // All buttons are visible to everyone

            // Register QR Code button click
            registerBtn.addEventListener('click', function() {
                showLoading();
                fetch('generate_qr.php?type=register')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayQRCode(data.qr_data, 'Registration QR Code', 'Scan this QR code to register your device');
                        } else {
                            showError(data.message || 'Failed to generate registration QR code');
                        }
                    })
                    .catch(error => {
                        showError('Error generating registration QR code');
                        console.error('Error:', error);
                    });
            });

            // Attendance QR Code button click
            attendanceBtn.addEventListener('click', function() {
                showLoading();
                fetch('generate_qr.php?type=attendance')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayQRCode(data.qr_data, 'Attendance QR Code', 'Scan this QR code with your registered device for clock in/out');
                        } else {
                            showError(data.message || 'Failed to generate attendance QR code');
                        }
                    })
                    .catch(error => {
                        showError('Error generating attendance QR code');
                        console.error('Error:', error);
                    });
            });

            function showLoading() {
                qrSection.style.display = 'block';
                qrContent.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
                        <p>Generating QR Code...</p>
                    </div>
                `;
            }

            function displayQRCode(qrData, title, description) {
                qrContent.innerHTML = `
                    <h2 class="qr-title">${title}</h2>
                    <p style="color: #7f8c8d; margin-bottom: 20px;">${description}</p>
                    <img src="${qrData.image_path}" alt="${title}" class="qr-code-img" style="max-width: 300px; height: auto;">
                    <div class="qr-info">
                        <div class="info-row">
                            <span class="info-label">Generated:</span>
                            <span class="info-value">${new Date().toLocaleString()}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Valid Until:</span>
                            <span class="info-value">${qrData.expires || 'N/A'}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">QR Code ID:</span>
                            <span class="info-value">${qrData.code ? qrData.code.substring(0, 8) : 'N/A'}</span>
                        </div>
                    </div>
                    <button onclick="hideQRCode()" style="margin-top: 20px; padding: 10px 20px; background: #95a5a6; color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
                `;
            }

            function showError(message) {
                qrContent.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #e74c3c;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                        <p>${message}</p>
                        <button onclick="hideQRCode()" style="margin-top: 20px; padding: 10px 20px; background: #95a5a6; color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
                    </div>
                `;
            }

            // Make hideQRCode globally available
            window.hideQRCode = function() {
                qrSection.style.display = 'none';
            };
        });

        // Update time display
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.title = 'NCHM Attendance - ' + timeString;
        }

        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
