# DHM Attendance System - Setup Guide

## Overview
This is a secure QR code-based staff attendance system for the Department of Hydro-Meteorology (DHM) that runs on a local server with IP address *************.

## System Requirements

### Server Requirements
- PHP 7.4 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- Composer (for PHP dependencies)
- Cron job support

### Network Requirements
- Server IP: *************
- Office Wi-Fi range: ***********-255
- No internet access required (local network only)

## Installation Steps

### 1. Database Setup

1. Import the existing database:
```sql
mysql -u root -p < attendance_db.sql
```

2. Run the enhanced schema:
```sql
mysql -u root -p attendance_db < database/enhanced_schema.sql
```

3. Update database credentials in `config/database.php`:
```php
private $host = '*************';
private $db_name = 'attendance_db';
private $username = 'your_db_username';
private $password = 'your_db_password';
```

### 2. Install PHP Dependencies

```bash
cd /path/to/attendance
composer install
```

### 3. Configure Email Settings

Update `config/database.php` with your email settings:
```php
const SMTP_HOST = 'smtp.gmail.com';
const SMTP_USERNAME = '<EMAIL>';
const SMTP_PASSWORD = 'your-app-password';
```

### 4. Set Up Directory Permissions

```bash
chmod 755 qr_codes/
chmod 755 logs/
mkdir -p logs qr_codes
chown -R www-data:www-data qr_codes/ logs/
```

### 5. Configure Web Server

#### Apache Configuration
```apache
<VirtualHost *************:80>
    DocumentRoot /path/to/attendance
    ServerName *************
    
    <Directory /path/to/attendance>
        AllowOverride All
        Require ip ***********/24
        Require ip *************
    </Directory>
</VirtualHost>
```

#### Nginx Configuration
```nginx
server {
    listen *************:80;
    root /path/to/attendance;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
    }
    
    # IP restrictions
    allow ***********/24;
    allow *************;
    deny all;
}
```

### 6. Set Up Cron Job for Daily Reports

Add to crontab:
```bash
# Daily report at 6:30 PM
30 18 * * * php /path/to/attendance/cron/daily_report.php

# Or run every minute to check for report time
* * * * * php /path/to/attendance/cron/daily_report.php
```

### 7. Default Admin Account

Default super admin credentials:
- Username: `superadmin`
- Password: `admin123`

**IMPORTANT: Change this password immediately after first login!**

## Usage Instructions

### For Staff Members

#### First Time Registration:
1. Get unique registration QR code from admin
2. Scan QR code with your phone
3. Enter device model information
4. Complete registration to get registration ID and device token

#### Daily Attendance:
1. Connect to office Wi-Fi (172.31.18.x)
2. Scan daily attendance QR code
3. Enter your registration ID and device token
4. System automatically determines clock-in or clock-out

### For Administrators

#### Admin Dashboard Access:
- URL: `http://*************/admin/login.php`
- Use admin credentials to login

#### Key Features:
- **Dashboard**: View statistics and recent attendance
- **Staff Management**: Add/edit staff, generate registration QR codes
- **Attendance Records**: View and export attendance data
- **Reports**: Generate and email daily reports
- **QR Codes**: Manage daily and registration QR codes

#### Generate Registration QR Codes:
1. Go to Staff Management
2. Find staff member who needs registration
3. Click "QR Code" button
4. Share generated QR code with staff member

#### Daily Reports:
- Automatically sent at 6:30 PM to configured emails
- Manual generation available in Reports section
- Export to Excel format available

## Security Features

### Device Verification:
- Unique device fingerprinting
- Device token validation
- Single device per staff member
- IP range validation

### Anti-Proxy Measures:
- IP range restrictions (172.31.18.x and *************)
- Device fingerprint matching
- Rate limiting (max 10 attempts per hour)
- Suspicious activity logging

### Access Control:
- Admin role-based permissions
- Session timeout (1 hour)
- Login attempt limiting
- Comprehensive audit logging

## Troubleshooting

### Common Issues:

1. **QR Code Generation Fails**
   - Check if `qr_codes/` directory exists and is writable
   - Verify Composer dependencies are installed

2. **Email Reports Not Sending**
   - Check email configuration in `config/database.php`
   - Verify cron job is running
   - Check logs in `logs/cron.log`

3. **Device Registration Fails**
   - Ensure user is on office Wi-Fi (172.31.18.x)
   - Check if staff member already has registered device
   - Verify QR code hasn't expired

4. **Attendance Recording Fails**
   - Verify device is registered
   - Check IP address is in allowed range
   - Ensure device fingerprint matches

### Log Files:
- System logs: Database `system_logs` table
- Cron logs: `logs/cron.log`
- Web server logs: Check Apache/Nginx error logs

## Maintenance

### Regular Tasks:
1. **Daily**: Monitor attendance reports
2. **Weekly**: Review security logs
3. **Monthly**: Clean old QR codes and logs
4. **Quarterly**: Update admin passwords

### Database Maintenance:
```sql
-- Clean old QR codes (older than 30 days)
DELETE FROM qr_codes WHERE created_date < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean old system logs (older than 90 days)
DELETE FROM system_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## Support

For technical support or issues:
1. Check system logs for error details
2. Verify network connectivity and IP ranges
3. Ensure all dependencies are installed
4. Contact system administrator

## Color Scheme (Meteorology & Hydrology Theme)

The system uses colors related to meteorology and hydrology:
- **Primary Blue**: #2c5aa0 (Deep sky)
- **Water Blue**: #1e88e5 (Ocean blue)
- **Sky Blue**: #87ceeb (Light sky)
- **Cloud Gray**: #b0bec5 (Cloud color)
- **Success Green**: #4caf50 (Nature green)
- **Warning Orange**: #ff9800 (Sunset orange)
- **Error Red**: #f44336 (Alert red)

This creates a professional, weather-themed interface appropriate for the Department of Hydro-Meteorology.
