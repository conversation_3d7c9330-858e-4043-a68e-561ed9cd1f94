<?php
/**
 * Daily Report Cron Job
 * This script should be run every minute via cron to check if it's time to send the daily report
 * 
 * Cron job entry (run every minute):
 * * * * * * php /path/to/attendance/cron/daily_report.php
 */

// Prevent direct web access
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('This script can only be run from command line');
}

require_once dirname(__DIR__) . '/classes/EmailReporter.php';
require_once dirname(__DIR__) . '/classes/DatabaseManager.php';

$email_reporter = new EmailReporter();
$db = new DatabaseManager();

// Log script execution
$log_message = "[" . date('Y-m-d H:i:s') . "] Daily report cron job executed\n";
file_put_contents(dirname(__DIR__) . '/logs/cron.log', $log_message, FILE_APPEND | LOCK_EX);

try {
    // Check if it's time to send the daily report
    if ($email_reporter->shouldSendDailyReport()) {
        $today = date('Y-m-d');
        
        // Check if report already sent today
        if (!$email_reporter->isReportSentToday($today)) {
            echo "Sending daily report for $today...\n";
            
            $result = $email_reporter->sendDailyReport($today);
            
            if ($result['success']) {
                echo "Daily report sent successfully!\n";
                $log_message = "[" . date('Y-m-d H:i:s') . "] Daily report sent successfully for $today\n";
            } else {
                echo "Failed to send daily report: " . $result['message'] . "\n";
                $log_message = "[" . date('Y-m-d H:i:s') . "] Failed to send daily report for $today: " . $result['message'] . "\n";
            }
            
            // Log the result
            file_put_contents(dirname(__DIR__) . '/logs/cron.log', $log_message, FILE_APPEND | LOCK_EX);
            
            // Log system action
            $db->logAction('admin_action', 'Daily report email', 
                json_encode(['date' => $today, 'status' => $result['success'] ? 'sent' : 'failed']),
                null, null, 'cron', 'cron-job');
                
        } else {
            echo "Daily report already sent for $today\n";
        }
    } else {
        echo "Not time to send daily report yet. Current time: " . date('H:i') . ", Report time: " . EmailConfig::REPORT_TIME . "\n";
    }
    
} catch (Exception $e) {
    $error_message = "[" . date('Y-m-d H:i:s') . "] Error in daily report cron: " . $e->getMessage() . "\n";
    file_put_contents(dirname(__DIR__) . '/logs/cron.log', $error_message, FILE_APPEND | LOCK_EX);
    echo "Error: " . $e->getMessage() . "\n";
}

echo "Cron job completed at " . date('Y-m-d H:i:s') . "\n";
?>
