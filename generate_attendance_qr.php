<?php
session_start();
require_once 'config/database.php';
require_once 'classes/DatabaseManager.php';

// Check if user is admin
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: admin_login.php');
    exit();
}

// Initialize database
$database = new Database();
$db = new DatabaseManager($database->getConnection());

$message = '';
$error = '';
$qr_codes = [];

// Get server IP for QR code links
$server_ip = $_SERVER['SERVER_ADDR'] ?? '*************';
$base_url = "http://{$server_ip}:8000";

// Handle QR code generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'generate_all') {
        // Generate attendance QR codes for all registered staff
        try {
            $registered_staff = $db->getRegisteredStaff();
            
            if (empty($registered_staff)) {
                $error = 'No registered staff found.';
            } else {
                foreach ($registered_staff as $staff) {
                    // Generate daily token for this staff member
                    $token = hash('sha256', $staff['id'] . $staff['registration_id'] . date('Y-m-d'));
                    
                    // Create attendance URL
                    $attendance_url = $base_url . '/staff_attendance.php?staff_id=' . $staff['id'] . '&token=' . $token;
                    
                    // Generate QR code
                    $qr_code = [
                        'staff_id' => $staff['id'],
                        'staff_name' => $staff['name'],
                        'eid_cid_permit' => $staff['eid_cid_permit'],
                        'url' => $attendance_url,
                        'qr_image' => generateQRCodeImage($attendance_url, $staff['id'])
                    ];
                    
                    $qr_codes[] = $qr_code;
                }
                
                $message = 'Generated ' . count($qr_codes) . ' attendance QR codes successfully!';
            }
        } catch (Exception $e) {
            $error = 'Failed to generate QR codes: ' . $e->getMessage();
        }
    } elseif ($action === 'generate_single') {
        $staff_id = $_POST['staff_id'] ?? '';
        
        if (empty($staff_id)) {
            $error = 'Please select a staff member.';
        } else {
            try {
                $staff = $db->getStaffById($staff_id);
                
                if (!$staff || !$staff['is_registered']) {
                    $error = 'Staff member not found or not registered.';
                } else {
                    // Generate daily token for this staff member
                    $token = hash('sha256', $staff['id'] . $staff['registration_id'] . date('Y-m-d'));
                    
                    // Create attendance URL
                    $attendance_url = $base_url . '/staff_attendance.php?staff_id=' . $staff['id'] . '&token=' . $token;
                    
                    // Generate QR code
                    $qr_code = [
                        'staff_id' => $staff['id'],
                        'staff_name' => $staff['name'],
                        'eid_cid_permit' => $staff['eid_cid_permit'],
                        'url' => $attendance_url,
                        'qr_image' => generateQRCodeImage($attendance_url, $staff['id'])
                    ];
                    
                    $qr_codes[] = $qr_code;
                    $message = 'Generated attendance QR code for ' . $staff['name'] . ' successfully!';
                }
            } catch (Exception $e) {
                $error = 'Failed to generate QR code: ' . $e->getMessage();
            }
        }
    }
}

// Function to generate QR code image
function generateQRCodeImage($data, $staff_id) {
    $filename = 'qr_codes/attendance_staff_' . $staff_id . '_' . date('Ymd') . '.png';
    
    if (!is_dir('qr_codes')) {
        mkdir('qr_codes', 0755, true);
    }
    
    // Use QR Server API to generate QR code
    $qr_url = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($data);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'method' => 'GET',
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $qr_image_data = file_get_contents($qr_url, false, $context);
    
    if ($qr_image_data !== false) {
        file_put_contents($filename, $qr_image_data);
        return $filename;
    } else {
        // Fallback: create a simple text file with the URL
        file_put_contents($filename . '.txt', $data);
        return $filename . '.txt';
    }
}

// Get all registered staff for dropdown
$registered_staff = $db->getRegisteredStaff();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Attendance QR Codes - NCHM Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .qr-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .qr-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .qr-card p {
            color: #6c757d;
            margin-bottom: 15px;
        }

        .qr-image {
            max-width: 200px;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .qr-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            word-break: break-all;
            color: #495057;
            margin-bottom: 15px;
        }

        .back-link {
            display: inline-block;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-top: 20px;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .qr-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Generate Attendance QR Codes</h1>
            <p>Create QR codes for staff attendance with IP address links</p>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <strong>✅ Success:</strong> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <strong>⚠️ Error:</strong> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <div class="form-section">
            <h2>Generate QR Codes</h2>
            
            <form method="POST">
                <button type="submit" name="action" value="generate_all" class="btn btn-success">
                    🎯 Generate QR Codes for All Registered Staff
                </button>
            </form>

            <hr style="margin: 20px 0;">

            <form method="POST">
                <div class="form-group">
                    <label for="staff_id">Or Generate for Specific Staff:</label>
                    <select name="staff_id" id="staff_id" required>
                        <option value="">Select Staff Member</option>
                        <?php foreach ($registered_staff as $staff): ?>
                            <option value="<?php echo $staff['id']; ?>">
                                <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['eid_cid_permit'] . ')'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="action" value="generate_single" class="btn btn-primary">
                    📱 Generate Single QR Code
                </button>
            </form>
        </div>

        <?php if (!empty($qr_codes)): ?>
            <div class="qr-grid">
                <?php foreach ($qr_codes as $qr): ?>
                    <div class="qr-card">
                        <h3><?php echo htmlspecialchars($qr['staff_name']); ?></h3>
                        <p><strong><?php echo htmlspecialchars($qr['eid_cid_permit']); ?></strong></p>
                        
                        <?php if (file_exists($qr['qr_image']) && pathinfo($qr['qr_image'], PATHINFO_EXTENSION) === 'png'): ?>
                            <img src="<?php echo $qr['qr_image']; ?>" alt="QR Code" class="qr-image">
                        <?php else: ?>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
                                <p>QR Code Image</p>
                                <small>Scan with camera app</small>
                            </div>
                        <?php endif; ?>
                        
                        <div class="qr-url">
                            <strong>URL:</strong><br>
                            <?php echo htmlspecialchars($qr['url']); ?>
                        </div>
                        
                        <button onclick="copyToClipboard('<?php echo htmlspecialchars($qr['url']); ?>')" class="btn btn-primary">
                            📋 Copy URL
                        </button>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <a href="admin_dashboard.php" class="back-link">← Back to Admin Dashboard</a>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('URL copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('URL copied to clipboard!');
                } catch (err) {
                    alert('Failed to copy URL');
                }
                document.body.removeChild(textArea);
            });
        }
    </script>
</body>
</html>
