-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 08, 2025 at 04:26 PM
-- Server version: 8.4.3
-- PHP Version: 8.3.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `attendance_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `attendance_logs`
--

CREATE TABLE `attendance_logs` (
  `id` int NOT NULL,
  `staff_id` int NOT NULL,
  `action` enum('clock_in','clock_out') NOT NULL,
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `staff`
--

CREATE TABLE `staff` (
  `id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `eid_cid_permit` varchar(50) NOT NULL,
  `id_type` enum('CID','Permit','EID') NOT NULL,
  `position_title` varchar(100) DEFAULT NULL,
  `division` varchar(100) DEFAULT NULL,
  `device_token` varchar(255) DEFAULT NULL,
  `device_model` varchar(100) DEFAULT NULL,
  `registered_date` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `staff`
--

INSERT INTO `staff` (`id`, `name`, `eid_cid_permit`, `id_type`, `position_title`, `division`, `device_token`, `device_model`, `registered_date`) VALUES
(223, 'Aita Singh Tamang', '20200115549', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(224, 'Ajay Pradhan', '9107020', 'EID', 'Meteorology/Hydrology Technician I', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(225, 'Amber Bahadur Rana', '202406927798', 'EID', 'Meteorology/Hydrology Technician III', 'CSD', NULL, NULL, '2025-07-08 10:24:52'),
(226, 'Arun Puri', '202501929730', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, '2025-07-08 10:24:52'),
(227, 'Chechey', '200508011', 'EID', 'Admin. Asst. I', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(228, 'Chencho Dema', '202401925685', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(229, 'Chimi Namgyel', '20170107857', 'EID', 'Sr. Statistical Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(230, 'Choki Wangmo', '202401926421', 'EID', 'ICT Technical Associate II', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(231, 'Dawa Yangki', '200508221', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(232, 'Dawa Yangchen', '201009126', 'EID', 'Meteorology/Hydrology Technician I', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(233, 'Dechen Wangmo', '20120200082', 'EID', 'Personal Asst. I', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(234, 'Dungchu Wangdi', '2006044', 'EID', 'Sr. Store Keeper VI', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(235, 'Harka Bdr. Subba', '200407344', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(236, 'Jamyang Zangpo', '20170107988', 'EID', 'Sr. Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(237, 'Jamyang Phuntshok', '200201064', 'EID', 'Principal Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(238, 'Jigme Namgyel', '202401925723', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(239, 'Jigme Wangdi', '200408043', 'EID', 'Meteorology/Hydrology Technician III', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(240, 'Kajur Tenzin', '200408040', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(241, 'Karma', '9909032', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(242, 'Karma Tenzin', '8908009', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(243, 'Karma Dupchu', '9801010', 'EID', 'Director', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(244, 'Karma', '9611009', 'EID', 'Specialist II', 'CSD', NULL, NULL, '2025-07-08 10:24:52'),
(245, 'Kezang Jigme', '202403926844', 'EID', 'Asst. Program Officer', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(246, 'Kinley Namgyel', '201002002', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(247, 'Kinley Tenzin', '202501929451', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(248, 'Kinley Tenzin', '202307924193', 'EID', 'Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(249, 'Kuenzang', '9907159', 'EID', 'Asst. Engineer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(250, 'Leki Wangdi', '200204120', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(251, 'Monju Subba', '20170107981', 'EID', 'Sr. Meteorology/Hydrology Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(252, 'Norbu Wangdi', '200204031', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(253, 'Pashupati Sharma', '8808020', 'EID', 'Principal Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(254, 'Pema Syldon', '20160106525', 'EID', 'Sr. Meteorology/Hydrology Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(255, 'Pema Yangden', '9808018', 'EID', 'Sr. Admin. Asst. V', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(256, 'Pema Dorji', '200811013', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(257, 'Pema Dorji', '200811018', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(258, 'Phuntsho Tshering', '200801071', 'EID', 'Principal Meteorology/Hydrology Officer', 'CSD', NULL, NULL, '2025-07-08 10:24:52'),
(259, 'Phurpa Wangdi', '8704026', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(260, 'Ranjit Tamang', '8908010', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(261, 'Renu Kumari Thapa', '9505029', 'EID', 'Sr. Laboratory Asst. IV', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(262, 'Rinchen Namgay', '9305034', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(263, 'Sangay Wangmo', '20200115546', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(264, 'Sangay Tenzin', '9901165', 'EID', 'Asst. Engineer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(265, 'Sangay Tshering', '202301923016', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(266, 'Saroj Acharya', '20200116264', 'EID', 'Sr. Meteorology/Hydrology Officer', 'TSRD', NULL, NULL, '2025-07-08 10:24:52'),
(267, 'Sherub Phuntsho', '200701045', 'EID', 'Principal Meteorology/Hydrology Officer', 'TSRD', NULL, NULL, '2025-07-08 10:24:52'),
(268, 'Singay Dorji', '2101065', 'EID', 'Specialist III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(269, 'Sonam Zangmo', '9505027', 'EID', 'Sr. Laboratory Asst. V', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(270, 'Sonam Tshewang', '9704025', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(271, 'Sonam Lhamo', '201001106', 'EID', 'Principal Meteorology/Hydrology Officer', 'CSD', NULL, NULL, '2025-07-08 10:24:52'),
(272, 'Sonam Dorji', '20170108483', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(273, 'Sonam Tashi', '20130702235', 'EID', 'Meteorology/Hydrology Technician III', 'TSRD', NULL, NULL, '2025-07-08 10:24:52'),
(274, 'Sonam Tshering', '20130902756', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(275, 'Sonam Wangmo', '20121200995', 'EID', 'Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(276, 'Sonam Tenzin Yuedsel', '202301923169', 'EID', 'ICT Technical Associate II', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(277, 'Sonam Lhamo', '20200115547', 'EID', 'Meteorology/Hydrology Technician IV', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(278, 'Tandin Wangchuk', '20150105022', 'EID', 'Dy. Chief Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(279, 'Tashi Jamtsho', '20120200081', 'EID', 'Driver', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(280, 'Thinley Gyelmo', '9901225', 'EID', 'Sr. Admin. Asst. V', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(281, 'Trashi Namgyal', '201201111', 'EID', 'Dy. Chief Meteorology/Hydrology Officer', 'TSRD', NULL, NULL, '2025-07-08 10:24:52'),
(282, 'Tshering Choden', '20120100419', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(283, 'Tshewang Jamtsho', '202401925672', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, '2025-07-08 10:24:52'),
(284, 'Tshewang Gyeltshen', '202501929469', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(285, 'Sudhi Janardhanan Radhamany', '1414103831514945', 'Permit', 'Technical Maintenance Officer', 'FWS', NULL, NULL, '2025-07-08 10:24:52'),
(286, 'Ugyen Tshering', '202307924314', 'EID', 'Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(287, 'Ugyen Dema', '202406927976', 'EID', 'Asst. Procurement Officer', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(288, 'Ugyen Chophel', '201201032', 'EID', 'Dy. Chief Statistical Officer', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(289, 'Ugyen Tshomo', '20140103335', 'EID', 'Sr. HR Officer', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(290, 'Wangchuk Dema', '200312012', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(291, 'Wangdi', '200508228', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(292, 'Yangchen', '200805076', 'EID', 'Admin. Asst. I', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(293, 'Yeshi Choki', '20190112967', 'EID', 'Sr. Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, '2025-07-08 10:24:52'),
(294, 'Yeshi Wangmo', '200508211', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, '2025-07-08 10:24:52'),
(295, 'Dig Maya Ghalley', '11209000646', 'CID', 'Sweeper', 'SECT', NULL, NULL, '2025-07-08 10:24:52'),
(296, 'Thinley Phuntsho', '10603000810', 'CID', 'Project Manager', 'MSD', NULL, NULL, '2025-07-08 10:24:52');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `attendance_logs`
--
ALTER TABLE `attendance_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `staff_id` (`staff_id`);

--
-- Indexes for table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `eid_cid_permit` (`eid_cid_permit`),
  ADD UNIQUE KEY `device_token` (`device_token`),
  ADD UNIQUE KEY `device_token_2` (`device_token`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `attendance_logs`
--
ALTER TABLE `attendance_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `staff`
--
ALTER TABLE `staff`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=297;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `attendance_logs`
--
ALTER TABLE `attendance_logs`
  ADD CONSTRAINT `attendance_logs_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
