<?php
session_start();
require_once 'config/database.php';
require_once 'classes/DatabaseManager.php';

// Initialize database
$database = new Database();
$db = new DatabaseManager($database->getConnection());

// Get client IP
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

$client_ip = getClientIP();
$message = '';
$error = '';
$staff_info = null;
$last_attendance = null;

// Get staff ID from URL parameter
$staff_id = $_GET['staff_id'] ?? '';
$token = $_GET['token'] ?? '';

if (empty($staff_id) || empty($token)) {
    $error = 'Invalid attendance link. Please scan a valid QR code.';
} else {
    // Validate token and get staff info
    $staff_info = $db->getStaffById($staff_id);

    if (!$staff_info) {
        $error = 'Staff member not found.';
    } else {
        // Validate that staff is registered
        if (!$staff_info['is_registered']) {
            $error = 'Device not registered. Please register your device first.';
        } else {
            // Validate token (simple token validation - you can enhance this)
            $expected_token = hash('sha256', $staff_id . $staff_info['registration_id'] . date('Y-m-d'));
            if ($token !== $expected_token) {
                $error = 'Invalid or expired attendance link.';
            } else {
                // Get last attendance to determine next action
                $last_attendance = $db->getLastAttendance($staff_id);
            }
        }
    }
}

// Handle attendance action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error && $staff_info) {
    $action = $_POST['action'] ?? '';

    if ($action === 'clock_in' || $action === 'clock_out') {
        // Validate IP range
        if (!$db->validateIPRange($client_ip)) {
            $error = 'Access denied. Please connect to the office Wi-Fi network.';
        } else {
            // Record attendance
            if ($db->recordAttendance($staff_id, $action)) {
                // Log attendance
                $db->logAction('attendance', ucfirst(str_replace('_', ' ', $action)),
                    json_encode(['staff_id' => $staff_id, 'action' => $action]),
                    $staff_id, null, $client_ip, $_SERVER['HTTP_USER_AGENT']);

                $message = ucfirst(str_replace('_', ' ', $action)) . ' successful!';

                // Update last attendance for display
                $last_attendance = $db->getLastAttendance($staff_id);
            } else {
                $error = 'Failed to record attendance. Please try again.';
            }
        }
    }
    // Removed the "Invalid action" error to prevent it from showing unnecessarily
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Attendance - NCHM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: white;
        }

        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }

        .staff-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            text-align: left;
        }

        .staff-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-value {
            color: #6c757d;
        }

        .attendance-buttons {
            display: flex;
            gap: 15px;
            margin: 25px 0;
        }

        .btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-clock-in {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-clock-in:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .btn-clock-out {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }

        .btn-clock-out:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .status-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .current-time {
            color: #6c757d;
            font-size: 14px;
            margin-top: 15px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            .attendance-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏢</div>
            <h1>Staff Attendance</h1>
            <p class="subtitle">NCHM Attendance System</p>
        </div>

        <?php if ($error): ?>
            <div class="error">
                <strong>⚠️ Error:</strong><br>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <a href="index.php" class="back-link">← Back to Main Page</a>
        <?php elseif ($message): ?>
            <div class="success">
                <strong>✅ Success:</strong><br>
                <?php echo htmlspecialchars($message); ?>
            </div>
            <script>
                // Play voice feedback based on the action
                function playVoiceFeedback() {
                    const message = "<?php echo htmlspecialchars($message); ?>";
                    let speechText = "";

                    if (message.includes("Clock in")) {
                        speechText = "Clock in successful";
                    } else if (message.includes("Clock out")) {
                        speechText = "Thank you";
                    }

                    if (speechText && 'speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(speechText);
                        utterance.rate = 0.8;
                        utterance.pitch = 1;
                        utterance.volume = 0.8;
                        speechSynthesis.speak(utterance);
                    }
                }

                // Play voice feedback immediately
                playVoiceFeedback();

                // Auto-refresh after 3 seconds to update status
                setTimeout(function() {
                    window.location.reload();
                }, 3000);
            </script>
        <?php endif; ?>

        <?php if ($staff_info && !$error): ?>
            <div class="staff-info">
                <h3>👤 Staff Information</h3>
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    <span class="info-value"><?php echo htmlspecialchars($staff_info['name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label"><?php echo htmlspecialchars($staff_info['id_type']); ?>:</span>
                    <span class="info-value"><?php echo htmlspecialchars($staff_info['eid_cid_permit']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Position:</span>
                    <span class="info-value"><?php echo htmlspecialchars($staff_info['position_title'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Division:</span>
                    <span class="info-value"><?php echo htmlspecialchars($staff_info['division'] ?? 'N/A'); ?></span>
                </div>
            </div>

            <?php if ($last_attendance): ?>
                <div class="status-info">
                    <strong>Last Action:</strong> 
                    <?php echo ucfirst(str_replace('_', ' ', $last_attendance['action'])); ?> 
                    at <?php echo date('H:i:s', strtotime($last_attendance['timestamp'])); ?>
                    on <?php echo date('M d, Y', strtotime($last_attendance['timestamp'])); ?>
                </div>
            <?php endif; ?>

            <?php if (!$message): ?>
                <form method="POST" id="attendanceForm">
                    <div class="attendance-buttons">
                        <?php
                        $next_action = (!$last_attendance || $last_attendance['action'] === 'clock_out') ? 'clock_in' : 'clock_out';
                        $can_clock_in = (!$last_attendance || $last_attendance['action'] === 'clock_out');
                        $can_clock_out = ($last_attendance && $last_attendance['action'] === 'clock_in');
                        ?>
                        
                        <button type="submit" name="action" value="clock_in" 
                                class="btn btn-clock-in" 
                                <?php echo !$can_clock_in ? 'disabled' : ''; ?>>
                            🕐 Clock In
                        </button>
                        
                        <button type="submit" name="action" value="clock_out" 
                                class="btn btn-clock-out"
                                <?php echo !$can_clock_out ? 'disabled' : ''; ?>>
                            🕐 Clock Out
                        </button>
                    </div>
                </form>
            <?php endif; ?>

            <div class="current-time">
                Current Time: <span id="currentTime"></span>
            </div>
        <?php endif; ?>

        <a href="index.php" class="back-link">← Back to Main Page</a>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // Update time every second
        updateTime();
        setInterval(updateTime, 1000);

        // Add confirmation for attendance actions
        document.getElementById('attendanceForm')?.addEventListener('submit', function(e) {
            const action = e.submitter.value;
            const actionText = action.replace('_', ' ');
            
            if (!confirm(`Are you sure you want to ${actionText}?`)) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
