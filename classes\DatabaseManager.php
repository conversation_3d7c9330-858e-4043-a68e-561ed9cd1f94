<?php
// Simple database configuration inline to avoid include issues
class SimpleDatabase {
    private $host = 'localhost';
    private $db_name = 'attendance_db';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        if ($this->conn === null) {
            try {
                $this->conn = new PDO(
                    "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                    $this->username,
                    $this->password,
                    array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
                );
            } catch(PDOException $exception) {
                throw new Exception("Connection error: " . $exception->getMessage());
            }
        }
        return $this->conn;
    }

    public function validateIPRange($ip) {
        // Allow all IPs for testing
        return true;
    }
}

class DatabaseManager {
    private $db;
    public $conn;

    public function __construct() {
        $this->db = new SimpleDatabase();
        $this->conn = $this->db->getConnection();
    }

    // Staff management methods
    public function getAllStaff() {
        $query = "SELECT * FROM staff ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getStaffById($id) {
        $query = "SELECT * FROM staff WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getRegisteredStaff() {
        $query = "SELECT * FROM staff WHERE is_registered = 1 ORDER BY name";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getStaffByEidCidPermit($eid_cid_permit) {
        $query = "SELECT * FROM staff WHERE eid_cid_permit = :eid_cid_permit";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':eid_cid_permit', $eid_cid_permit);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function checkStaffAlreadyRegistered($name, $eid_cid_permit) {
        $query = "SELECT * FROM staff WHERE name = :name AND eid_cid_permit = :eid_cid_permit AND is_registered = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':eid_cid_permit', $eid_cid_permit);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function updateStaffRegistration($staff_id, $registration_id, $device_token, $device_model) {
        $query = "UPDATE staff SET registration_id = :registration_id, device_token = :device_token, 
                  device_model = :device_model, is_registered = 1 WHERE id = :staff_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':registration_id', $registration_id);
        $stmt->bindParam(':device_token', $device_token);
        $stmt->bindParam(':device_model', $device_model);
        $stmt->bindParam(':staff_id', $staff_id);
        return $stmt->execute();
    }

    // Device registration methods
    public function createDeviceRegistration($staff_id, $registration_id, $device_token, $device_model, $device_fingerprint, $ip_address, $user_agent) {
        $query = "INSERT INTO device_registrations (staff_id, registration_id, device_token, device_model, 
                  device_fingerprint, ip_address, user_agent) VALUES (:staff_id, :registration_id, :device_token, 
                  :device_model, :device_fingerprint, :ip_address, :user_agent)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':registration_id', $registration_id);
        $stmt->bindParam(':device_token', $device_token);
        $stmt->bindParam(':device_model', $device_model);
        $stmt->bindParam(':device_fingerprint', $device_fingerprint);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->bindParam(':user_agent', $user_agent);
        return $stmt->execute();
    }

    public function getDeviceRegistration($registration_id, $device_token) {
        $query = "SELECT dr.*, s.name, s.eid_cid_permit FROM device_registrations dr 
                  JOIN staff s ON dr.staff_id = s.id 
                  WHERE dr.registration_id = :registration_id AND dr.device_token = :device_token AND dr.is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':registration_id', $registration_id);
        $stmt->bindParam(':device_token', $device_token);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function updateDeviceLastUsed($registration_id) {
        $query = "UPDATE device_registrations SET last_used = NOW() WHERE registration_id = :registration_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':registration_id', $registration_id);
        return $stmt->execute();
    }

    // QR Code methods
    public function createQRCode($qr_code, $qr_type, $staff_id = null, $expiry_hours = 24, $max_usage = null) {
        $expiry_date = date('Y-m-d H:i:s', strtotime("+{$expiry_hours} hours"));
        $query = "INSERT INTO qr_codes (qr_code, qr_type, staff_id, expiry_date, max_usage) 
                  VALUES (:qr_code, :qr_type, :staff_id, :expiry_date, :max_usage)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':qr_code', $qr_code);
        $stmt->bindParam(':qr_type', $qr_type);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':expiry_date', $expiry_date);
        $stmt->bindParam(':max_usage', $max_usage);
        return $stmt->execute();
    }

    public function getQRCode($qr_code) {
        $query = "SELECT * FROM qr_codes WHERE qr_code = :qr_code AND is_active = 1 AND expiry_date > NOW()";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':qr_code', $qr_code);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function incrementQRUsage($qr_code) {
        $query = "UPDATE qr_codes SET usage_count = usage_count + 1 WHERE qr_code = :qr_code";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':qr_code', $qr_code);
        return $stmt->execute();
    }

    // Attendance methods
    public function recordAttendance($staff_id, $action) {
        $query = "INSERT INTO attendance_logs (staff_id, action) VALUES (:staff_id, :action)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':action', $action);
        return $stmt->execute();
    }

    public function getLastAttendance($staff_id, $date = null) {
        if (!$date) $date = date('Y-m-d');
        $query = "SELECT * FROM attendance_logs WHERE staff_id = :staff_id AND DATE(timestamp) = :date 
                  ORDER BY timestamp DESC LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':date', $date);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getDailyAttendance($date = null) {
        if (!$date) $date = date('Y-m-d');
        $query = "SELECT s.name, s.eid_cid_permit, s.position_title, s.division,
                  al.action, al.timestamp
                  FROM attendance_logs al
                  JOIN staff s ON al.staff_id = s.id
                  WHERE DATE(al.timestamp) = :date
                  ORDER BY al.timestamp DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDailyAttendanceGrouped($date = null) {
        if (!$date) $date = date('Y-m-d');

        // Get all attendance records for the date
        $query = "SELECT s.name, s.eid_cid_permit, s.position_title, s.division,
                  al.action, al.timestamp, al.staff_id
                  FROM attendance_logs al
                  JOIN staff s ON al.staff_id = s.id
                  WHERE DATE(al.timestamp) = :date
                  ORDER BY s.name ASC, al.timestamp ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date', $date);
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Group by staff member
        $grouped = [];
        foreach ($records as $record) {
            $key = $record['staff_id'];
            if (!isset($grouped[$key])) {
                $grouped[$key] = [
                    'name' => $record['name'],
                    'eid_cid_permit' => $record['eid_cid_permit'],
                    'position_title' => $record['position_title'],
                    'division' => $record['division'],
                    'clock_in_time' => null,
                    'clock_out_time' => null,
                    'date' => $date,
                    'status' => 'Absent'
                ];
            }

            if ($record['action'] === 'clock_in') {
                $grouped[$key]['clock_in_time'] = date('H:i:s', strtotime($record['timestamp']));
                $grouped[$key]['status'] = 'Present';
            } elseif ($record['action'] === 'clock_out') {
                $grouped[$key]['clock_out_time'] = date('H:i:s', strtotime($record['timestamp']));
                if ($grouped[$key]['clock_in_time']) {
                    $grouped[$key]['status'] = 'Complete';
                }
            }
        }

        return array_values($grouped);
    }

    // System logging
    public function logAction($log_type, $action, $details = null, $staff_id = null, $admin_id = null, $ip_address = null, $user_agent = null) {
        $query = "INSERT INTO system_logs (log_type, action, details, staff_id, admin_id, ip_address, user_agent) 
                  VALUES (:log_type, :action, :details, :staff_id, :admin_id, :ip_address, :user_agent)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':log_type', $log_type);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':details', $details);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':admin_id', $admin_id);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->bindParam(':user_agent', $user_agent);
        return $stmt->execute();
    }

    // Admin user methods
    public function getAdminByUsername($username) {
        $query = "SELECT * FROM admin_users WHERE username = :username AND is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function updateAdminLogin($admin_id) {
        $query = "UPDATE admin_users SET last_login = NOW(), login_attempts = 0 WHERE id = :admin_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':admin_id', $admin_id);
        return $stmt->execute();
    }

    public function incrementLoginAttempts($username) {
        $query = "UPDATE admin_users SET login_attempts = login_attempts + 1 WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        return $stmt->execute();
    }

    // Utility methods
    public function validateIP($ip) {
        return $this->db->validateIPRange($ip);
    }

    public function generateUniqueId($prefix = '') {
        return $prefix . uniqid() . bin2hex(random_bytes(8));
    }
}
?>
