<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: simple_login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=attendance_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get statistics
    $stmt = $pdo->query("SELECT COUNT(*) FROM staff");
    $total_staff = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM staff WHERE is_registered = 1");
    $registered_staff = $stmt->fetchColumn();
    
    // Count unique staff members who have attendance records today (to match the table count)
    $stmt = $pdo->query("SELECT COUNT(DISTINCT staff_id) FROM attendance_logs WHERE DATE(timestamp) = CURDATE()");
    $today_attendance = $stmt->fetchColumn();
    
    // Get recent attendance (grouped by staff)
    $stmt = $pdo->query("
        SELECT s.name, s.eid_cid_permit, s.position_title, s.division,
               al.action, al.timestamp, al.staff_id
        FROM attendance_logs al
        JOIN staff s ON al.staff_id = s.id
        WHERE DATE(al.timestamp) = CURDATE()
        ORDER BY s.name ASC, al.timestamp ASC
    ");
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Group attendance by staff member
    $recent_attendance = [];
    foreach ($attendance_records as $record) {
        $key = $record['staff_id'];
        if (!isset($recent_attendance[$key])) {
            $recent_attendance[$key] = [
                'name' => $record['name'],
                'eid_cid_permit' => $record['eid_cid_permit'],
                'position_title' => $record['position_title'],
                'division' => $record['division'],
                'clock_in_time' => null,
                'clock_out_time' => null,
                'date' => date('Y-m-d'),
                'status' => 'Absent'
            ];
        }

        if ($record['action'] === 'clock_in') {
            $recent_attendance[$key]['clock_in_time'] = date('H:i:s', strtotime($record['timestamp']));
            $recent_attendance[$key]['status'] = 'Present';
        } elseif ($record['action'] === 'clock_out') {
            $recent_attendance[$key]['clock_out_time'] = date('H:i:s', strtotime($record['timestamp']));
            if ($recent_attendance[$key]['clock_in_time']) {
                $recent_attendance[$key]['status'] = 'Complete';
            }
        }
    }

    // Convert to indexed array and limit to 10 records
    $recent_attendance = array_slice(array_values($recent_attendance), 0, 10);
    
} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - NCHM Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logo {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }
        .main-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .card-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
        }
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        .card-content {
            padding: 2rem;
        }
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-right: 1rem;
        }
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th, .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn-print {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-excel {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-print:hover {
            background: #5a6268;
        }

        .btn-excel:hover {
            background: #218838;
        }

        @media print {
            .action-buttons, .header, .stats-grid {
                display: none !important;
            }
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-section">
            <div class="logo">NCHM</div>
            <div>
                <h2>NCHM Attendance System</h2>
                <p>National Center for Hydrology and Meteorology</p>
            </div>
        </div>
        <div>
            <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_name']); ?></span>
            <a href="logout.php" class="btn btn-danger">Logout</a>
        </div>
    </div>

    <div class="main-content">
        <h1>Dashboard Overview</h1>
        
        <?php if (isset($error)): ?>
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_staff ?? 0; ?></div>
                <div class="stat-label">Total Staff</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $registered_staff ?? 0; ?></div>
                <div class="stat-label">Registered Devices</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $today_attendance ?? 0; ?></div>
                <div class="stat-label">Today's Attendance</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_staff > 0 ? number_format(($registered_staff / $total_staff) * 100, 1) : 0; ?>%</div>
                <div class="stat-label">Registration Rate</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Quick Actions</h3>
            </div>
            <div class="card-content">
                <a href="../index.php" class="btn">View Main Page</a>
                <a href="staff_list.php" class="btn">Manage Staff</a>
                <a href="../basic_setup.php" class="btn">Run Setup Again</a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Attendance (Today)</h3>
            </div>
            <div class="card-content">
                <?php if (empty($recent_attendance)): ?>
                    <p>No attendance records for today.</p>
                <?php else: ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>EID/CID/Permit</th>
                                <th>Position</th>
                                <th>Division</th>
                                <th>Clock In Time</th>
                                <th>Clock Out Time</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_attendance as $record): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($record['name']); ?></td>
                                <td><?php echo htmlspecialchars($record['eid_cid_permit']); ?></td>
                                <td><?php echo htmlspecialchars($record['position_title'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($record['division'] ?? 'N/A'); ?></td>
                                <td><?php echo $record['clock_in_time'] ? htmlspecialchars($record['clock_in_time']) : '-'; ?></td>
                                <td><?php echo $record['clock_out_time'] ? htmlspecialchars($record['clock_out_time']) : '-'; ?></td>
                                <td><?php echo htmlspecialchars($record['date']); ?></td>
                                <td>
                                    <span class="badge <?php
                                        echo $record['status'] === 'Complete' ? 'badge-success' :
                                             ($record['status'] === 'Present' ? 'badge-warning' : 'badge-danger');
                                    ?>">
                                        <?php echo htmlspecialchars($record['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>

                <!-- Print and Excel Download Buttons -->
                <div class="action-buttons">
                    <button onclick="printAttendance()" class="btn-print">
                        🖨️ Print
                    </button>
                    <button onclick="exportToExcel()" class="btn-excel">
                        📊 Download Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function printAttendance() {
            // Create a new window for printing
            const printWindow = window.open('', '_blank');
            const today = new Date().toLocaleDateString();

            // Get the attendance table
            const table = document.querySelector('.table');
            const tableHTML = table ? table.outerHTML : '<p>No attendance records for today.</p>';

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>NCHM Attendance Report - ${today}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .logo { font-size: 24px; font-weight: bold; color: #2c5aa0; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f8f9fa; font-weight: bold; }
                        .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
                        .badge-success { background: #d4edda; color: #155724; }
                        .badge-warning { background: #fff3cd; color: #856404; }
                        .badge-danger { background: #f8d7da; color: #721c24; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="logo">NCHM</div>
                        <h2>National Center for Hydrology and Meteorology</h2>
                        <h3>Daily Attendance Report</h3>
                        <p>Date: ${today}</p>
                    </div>
                    ${tableHTML}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        function exportToExcel() {
            // Get today's date for filename
            const today = new Date().toISOString().split('T')[0];

            // Create Excel export URL
            const exportUrl = 'export_attendance.php?date=' + today + '&format=excel';

            // Create a temporary link and click it to download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = `NCHM_Attendance_${today}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
