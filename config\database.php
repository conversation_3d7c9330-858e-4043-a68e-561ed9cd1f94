<?php
// Database configuration for QR Attendance System
class Database {
    private $host;
    private $db_name = 'attendance_db';
    private $username = 'root';
    private $password = ''; // Laragon default
    private $conn;

    public function __construct() {
        // Auto-detect environment based on server IP
        $server_ip = $_SERVER['SERVER_ADDR'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost';

        if ($server_ip === '*************' || $_SERVER['HTTP_HOST'] === '*************') {
            // Network production environment
            $this->host = '*************';
        } else {
            // Local development environment
            $this->host = 'localhost';
        }
    }

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }

    public function getHost() {
        return $this->host;
    }

    public function validateIPRange($ip) {
        // Allow server IP and office Wi-Fi range (plus localhost for testing)
        $allowed_ranges = [
            '*************', // Server IP
            '***********/24', // Office Wi-Fi range
            '127.0.0.1', // Localhost
            '::1', // IPv6 localhost
            '***********/24' // Local network for testing
        ];
        
        foreach ($allowed_ranges as $range) {
            if ($ip === '*************') return true;
            if ($this->ipInRange($ip, $range)) return true;
        }
        return false;
    }

    private function ipInRange($ip, $range) {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $bits) = explode('/', $range);
        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet_long &= $mask;
        
        return ($ip_long & $mask) === $subnet_long;
    }
}

// Email configuration
class EmailConfig {
    const SMTP_HOST = 'smtp.gmail.com';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '<EMAIL>'; // Configure this
    const SMTP_PASSWORD = 'your-app-password'; // Configure this
    const FROM_EMAIL = '<EMAIL>';
    const FROM_NAME = 'NCHM Attendance System';
    
    // Daily report recipients
    const DAILY_REPORT_EMAILS = [
        '<EMAIL>',
        '<EMAIL>'
    ];
    
    // Report time (24-hour format)
    const REPORT_TIME = '18:30'; // 6:30 PM
}

// System configuration
class SystemConfig {
    const SERVER_IP = '*************';
    const OFFICE_WIFI_RANGE = '***********/24';
    const QR_CODE_EXPIRY_HOURS = 24; // Daily QR codes expire after 24 hours
    const REGISTRATION_QR_EXPIRY_DAYS = 30; // Registration QR codes expire after 30 days
    const MAX_LOGIN_ATTEMPTS = 5;
    const SESSION_TIMEOUT = 3600; // 1 hour
}
?>
