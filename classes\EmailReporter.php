<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/DatabaseManager.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

class EmailReporter {
    private $db;
    private $mailer;

    public function __construct() {
        $this->db = new DatabaseManager();
        $this->setupMailer();
    }

    private function setupMailer() {
        $this->mailer = new PHPMailer(true);
        
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = EmailConfig::SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = EmailConfig::SMTP_USERNAME;
            $this->mailer->Password = EmailConfig::SMTP_PASSWORD;
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = EmailConfig::SMTP_PORT;

            // Recipients
            $this->mailer->setFrom(EmailConfig::FROM_EMAIL, EmailConfig::FROM_NAME);
            
        } catch (Exception $e) {
            error_log("Mailer setup failed: " . $e->getMessage());
        }
    }

    /**
     * Generate daily attendance report
     */
    public function generateDailyReport($date = null) {
        if (!$date) $date = date('Y-m-d');
        
        $attendance_records = $this->db->getDailyAttendance($date);
        
        // Group records by staff
        $staff_attendance = [];
        foreach ($attendance_records as $record) {
            $staff_id = $record['eid_cid_permit'];
            if (!isset($staff_attendance[$staff_id])) {
                $staff_attendance[$staff_id] = [
                    'name' => $record['name'],
                    'eid_cid_permit' => $record['eid_cid_permit'],
                    'position_title' => $record['position_title'],
                    'division' => $record['division'],
                    'clock_in' => null,
                    'clock_out' => null,
                    'total_hours' => 0,
                    'status' => 'Absent'
                ];
            }
            
            if ($record['action'] === 'clock_in') {
                $staff_attendance[$staff_id]['clock_in'] = $record['timestamp'];
                $staff_attendance[$staff_id]['status'] = 'Present';
            } elseif ($record['action'] === 'clock_out') {
                $staff_attendance[$staff_id]['clock_out'] = $record['timestamp'];
            }
        }

        // Calculate total hours
        foreach ($staff_attendance as &$staff) {
            if ($staff['clock_in'] && $staff['clock_out']) {
                $clock_in_time = strtotime($staff['clock_in']);
                $clock_out_time = strtotime($staff['clock_out']);
                $staff['total_hours'] = round(($clock_out_time - $clock_in_time) / 3600, 2);
            }
        }

        // Get all staff to show absent ones
        $all_staff = $this->db->getAllStaff();
        foreach ($all_staff as $staff) {
            if (!isset($staff_attendance[$staff['eid_cid_permit']])) {
                $staff_attendance[$staff['eid_cid_permit']] = [
                    'name' => $staff['name'],
                    'eid_cid_permit' => $staff['eid_cid_permit'],
                    'position_title' => $staff['position_title'],
                    'division' => $staff['division'],
                    'clock_in' => null,
                    'clock_out' => null,
                    'total_hours' => 0,
                    'status' => 'Absent'
                ];
            }
        }

        return [
            'date' => $date,
            'attendance' => $staff_attendance,
            'summary' => [
                'total_staff' => count($all_staff),
                'present' => count(array_filter($staff_attendance, function($s) { return $s['status'] === 'Present'; })),
                'absent' => count(array_filter($staff_attendance, function($s) { return $s['status'] === 'Absent'; }))
            ]
        ];
    }

    /**
     * Generate HTML report
     */
    public function generateHTMLReport($report_data) {
        $date = date('F j, Y', strtotime($report_data['date']));
        $summary = $report_data['summary'];
        
        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Daily Attendance Report - $date</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; color: #2c3e50; }
                .header { text-align: center; margin-bottom: 30px; }
                .logo { color: #3498db; font-size: 24px; font-weight: bold; }
                .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
                .summary-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; text-align: center; }
                .summary-item { background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #3498db; }
                .summary-number { font-size: 24px; font-weight: bold; color: #3498db; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background: #3498db; color: white; }
                tr:nth-child(even) { background: #f8f9fa; }
                .status-present { color: #27ae60; font-weight: bold; }
                .status-absent { color: #e74c3c; font-weight: bold; }
                .footer { margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <div class='logo'>DHM</div>
                <h1>Daily Attendance Report</h1>
                <h2>Department of Hydro-Meteorology</h2>
                <p><strong>Date:</strong> $date</p>
            </div>
            
            <div class='summary'>
                <h3>Attendance Summary</h3>
                <div class='summary-grid'>
                    <div class='summary-item'>
                        <div class='summary-number'>{$summary['total_staff']}</div>
                        <div>Total Staff</div>
                    </div>
                    <div class='summary-item'>
                        <div class='summary-number'>{$summary['present']}</div>
                        <div>Present</div>
                    </div>
                    <div class='summary-item'>
                        <div class='summary-number'>{$summary['absent']}</div>
                        <div>Absent</div>
                    </div>
                </div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>EID/CID/Permit</th>
                        <th>Position</th>
                        <th>Division</th>
                        <th>Clock In</th>
                        <th>Clock Out</th>
                        <th>Total Hours</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($report_data['attendance'] as $staff) {
            $status_class = $staff['status'] === 'Present' ? 'status-present' : 'status-absent';
            $clock_in = $staff['clock_in'] ? date('H:i:s', strtotime($staff['clock_in'])) : '-';
            $clock_out = $staff['clock_out'] ? date('H:i:s', strtotime($staff['clock_out'])) : '-';
            $total_hours = $staff['total_hours'] > 0 ? $staff['total_hours'] . ' hrs' : '-';
            
            $html .= "
                <tr>
                    <td>{$staff['name']}</td>
                    <td>{$staff['eid_cid_permit']}</td>
                    <td>{$staff['position_title']}</td>
                    <td>{$staff['division']}</td>
                    <td>$clock_in</td>
                    <td>$clock_out</td>
                    <td>$total_hours</td>
                    <td class='$status_class'>{$staff['status']}</td>
                </tr>";
        }
        
        $html .= "
                </tbody>
            </table>
            
            <div class='footer'>
                <p>Generated on " . date('Y-m-d H:i:s') . " by DHM Attendance System</p>
                <p>Department of Hydro-Meteorology, Royal Government of Bhutan</p>
            </div>
        </body>
        </html>";
        
        return $html;
    }

    /**
     * Send daily report email
     */
    public function sendDailyReport($date = null) {
        if (!$date) $date = date('Y-m-d');
        
        try {
            // Generate report
            $report_data = $this->generateDailyReport($date);
            $html_report = $this->generateHTMLReport($report_data);
            
            // Clear previous recipients
            $this->mailer->clearAddresses();
            
            // Add recipients
            foreach (EmailConfig::DAILY_REPORT_EMAILS as $email) {
                $this->mailer->addAddress($email);
            }
            
            // Email content
            $formatted_date = date('F j, Y', strtotime($date));
            $this->mailer->Subject = "Daily Attendance Report - $formatted_date - DHM";
            $this->mailer->isHTML(true);
            $this->mailer->Body = $html_report;
            
            // Send email
            $this->mailer->send();
            
            // Log successful send
            $this->logEmailReport($date, 'daily_attendance', EmailConfig::DAILY_REPORT_EMAILS, 
                "Daily Attendance Report - $formatted_date", 'sent');
            
            return ['success' => true, 'message' => 'Daily report sent successfully'];
            
        } catch (Exception $e) {
            // Log failed send
            $this->logEmailReport($date, 'daily_attendance', EmailConfig::DAILY_REPORT_EMAILS, 
                "Daily Attendance Report - $formatted_date", 'failed', $e->getMessage());
            
            return ['success' => false, 'message' => 'Failed to send report: ' . $e->getMessage()];
        }
    }

    /**
     * Log email report
     */
    private function logEmailReport($date, $type, $recipients, $subject, $status, $error = null) {
        $stmt = $this->db->conn->prepare(
            "INSERT INTO email_reports (report_date, report_type, recipients, subject, status, error_message) 
             VALUES (?, ?, ?, ?, ?, ?)"
        );
        $stmt->execute([
            $date,
            $type,
            json_encode($recipients),
            $subject,
            $status,
            $error
        ]);
    }

    /**
     * Check if it's time to send daily report
     */
    public function shouldSendDailyReport() {
        $current_time = date('H:i');
        $report_time = EmailConfig::REPORT_TIME;
        
        // Check if current time matches report time (within 1 minute)
        $current_minutes = (int)date('H') * 60 + (int)date('i');
        $report_minutes = (int)substr($report_time, 0, 2) * 60 + (int)substr($report_time, 3, 2);
        
        return abs($current_minutes - $report_minutes) <= 1;
    }

    /**
     * Check if report already sent today
     */
    public function isReportSentToday($date = null) {
        if (!$date) $date = date('Y-m-d');
        
        $stmt = $this->db->conn->prepare(
            "SELECT COUNT(*) FROM email_reports WHERE report_date = ? AND report_type = 'daily_attendance' AND status = 'sent'"
        );
        $stmt->execute([$date]);
        
        return $stmt->fetchColumn() > 0;
    }
}
?>
