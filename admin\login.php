<?php
session_start();
require_once '../config/database.php';
require_once '../classes/DatabaseManager.php';

$db = new DatabaseManager();
$error = '';

// Redirect if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    header('Location: dashboard.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password';
    } else {
        $admin = $db->getAdminByUsername($username);
        
        if ($admin) {
            // Check if account is locked
            if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
                $error = 'Account is temporarily locked. Please try again later.';
            } elseif ($admin['login_attempts'] >= SystemConfig::MAX_LOGIN_ATTEMPTS) {
                // Lock account for 30 minutes
                $lock_until = date('Y-m-d H:i:s', strtotime('+30 minutes'));
                $stmt = $db->conn->prepare("UPDATE admin_users SET locked_until = :lock_until WHERE id = :id");
                $stmt->bindParam(':lock_until', $lock_until);
                $stmt->bindParam(':id', $admin['id']);
                $stmt->execute();
                
                $error = 'Too many failed attempts. Account locked for 30 minutes.';
            } elseif (password_verify($password, $admin['password_hash'])) {
                // Successful login
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['login_time'] = time();
                
                // Update last login and reset attempts
                $db->updateAdminLogin($admin['id']);
                
                // Log successful login
                $db->logAction('login', 'Admin login successful', 
                    json_encode(['username' => $username]),
                    null, $admin['id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
                
                header('Location: dashboard.php');
                exit;
            } else {
                // Failed login
                $db->incrementLoginAttempts($username);
                
                // Log failed login
                $db->logAction('login', 'Admin login failed', 
                    json_encode(['username' => $username, 'reason' => 'invalid_password']),
                    null, null, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
                
                $error = 'Invalid username or password';
            }
        } else {
            // Log failed login attempt
            $db->logAction('login', 'Admin login failed', 
                json_encode(['username' => $username, 'reason' => 'user_not_found']),
                null, null, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
            
            $error = 'Invalid username or password';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - NCHM Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: 500;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .footer {
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 14px;
        }

        .security-info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            color: #2c3e50;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🔐</div>
        <h1>Admin Login</h1>
        <p class="subtitle">NCHM Attendance System</p>

        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="btn">Login</button>
        </form>

        <div class="security-info">
            <strong>Security Notice:</strong><br>
            • Maximum 5 login attempts allowed<br>
            • Account will be locked for 30 minutes after failed attempts<br>
            • All login attempts are logged
        </div>

        <div class="footer">
            National Center for Hydrology and Meteorology<br>
            Royal Government of Bhutan
        </div>
    </div>

    <script>
        // Auto-focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
