<?php
session_start();
require_once '../config/database.php';
require_once '../classes/DatabaseManager.php';
require_once '../classes/QRCodeManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Check session timeout
if (time() - $_SESSION['login_time'] > SystemConfig::SESSION_TIMEOUT) {
    session_destroy();
    header('Location: login.php?timeout=1');
    exit;
}

$db = new DatabaseManager();
$qr_manager = new QRCodeManager();

// Get dashboard statistics
$total_staff = count($db->getAllStaff());
$registered_staff = $db->conn->query("SELECT COUNT(*) FROM staff WHERE is_registered = 1")->fetchColumn();
// Count unique staff members who have attendance records today (to match the table count)
$today_attendance = $db->conn->query("SELECT COUNT(DISTINCT staff_id) FROM attendance_logs WHERE DATE(timestamp) = CURDATE()")->fetchColumn();

// Get recent attendance (grouped by staff)
$recent_attendance = $db->getDailyAttendanceGrouped();
$recent_attendance = array_slice($recent_attendance, 0, 10); // Last 10 records

// Handle QR code generation (daily QR removed from dashboard)

// Handle staff registration QR generation
$registration_qr = null;
if (isset($_POST['generate_registration_qr']) && isset($_POST['staff_id'])) {
    $staff_id = $_POST['staff_id'];
    try {
        $registration_qr = $qr_manager->generateRegistrationQR($staff_id);
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$page_title = 'Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - NCHM Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #2c3e50;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-menu {
            background: white;
            border-bottom: 1px solid #e1e8ed;
            padding: 0 2rem;
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 2rem;
        }

        .nav-item {
            padding: 1rem 0;
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .nav-item:hover, .nav-item.active {
            color: #3498db;
            border-bottom-color: #3498db;
        }

        .main-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-content {
            padding: 2rem;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .qr-display {
            text-align: center;
            padding: 2rem;
        }

        .qr-code-img {
            max-width: 300px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e8ed;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .main-content {
                padding: 0 1rem;
            }
        }

        .action-buttons {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn-print {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-excel {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-print:hover {
            background: #5a6268;
        }

        .btn-excel:hover {
            background: #218838;
        }

        @media print {
            .action-buttons, .header, .stats-grid {
                display: none !important;
            }
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">NCHM</div>
                <div>
                    <h2>NCHM Attendance System</h2>
                    <p>National Center for Hydrology and Meteorology</p>
                </div>
            </div>
            <div class="user-info">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_name']); ?></span>
                <span class="badge badge-success"><?php echo ucfirst($_SESSION['admin_role']); ?></span>
                <a href="logout.php" class="btn btn-danger">Logout</a>
            </div>
        </div>
    </div>

    <nav class="nav-menu">
        <div class="nav-content">
            <a href="dashboard.php" class="nav-item active">Dashboard</a>
            <a href="staff.php" class="nav-item">Staff Management</a>
            <a href="attendance.php" class="nav-item">Attendance Records</a>
            <a href="reports.php" class="nav-item">Reports</a>
            <a href="qr-codes.php" class="nav-item">QR Codes</a>
            <?php if ($_SESSION['admin_role'] === 'super_admin'): ?>
                <a href="admin-users.php" class="nav-item">Admin Users</a>
            <?php endif; ?>
        </div>
    </nav>

    <div class="main-content">
        <h1>Dashboard Overview</h1>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_staff; ?></div>
                <div class="stat-label">Total Staff</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $registered_staff; ?></div>
                <div class="stat-label">Registered Devices</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $today_attendance; ?></div>
                <div class="stat-label">Today's Attendance</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format(($registered_staff / $total_staff) * 100, 1); ?>%</div>
                <div class="stat-label">Registration Rate</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Quick Actions</h3>
            </div>
            <div class="card-content">
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <a href="staff.php" class="btn">Manage Staff</a>
                    <a href="attendance.php" class="btn">View Attendance</a>
                    <a href="reports.php" class="btn">Generate Reports</a>
                </div>
            </div>
        </div>



        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Recent Attendance (Today)</h3>
                <a href="attendance.php" class="btn">View All</a>
            </div>
            <div class="card-content">
                <?php if (empty($recent_attendance)): ?>
                    <p>No attendance records for today.</p>
                <?php else: ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>EID/CID/Permit</th>
                                <th>Position</th>
                                <th>Division</th>
                                <th>Clock In Time</th>
                                <th>Clock Out Time</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_attendance as $record): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($record['name']); ?></td>
                                <td><?php echo htmlspecialchars($record['eid_cid_permit']); ?></td>
                                <td><?php echo htmlspecialchars($record['position_title'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($record['division'] ?? 'N/A'); ?></td>
                                <td><?php echo $record['clock_in_time'] ? htmlspecialchars($record['clock_in_time']) : '-'; ?></td>
                                <td><?php echo $record['clock_out_time'] ? htmlspecialchars($record['clock_out_time']) : '-'; ?></td>
                                <td><?php echo htmlspecialchars($record['date']); ?></td>
                                <td>
                                    <span class="badge <?php
                                        echo $record['status'] === 'Complete' ? 'badge-success' :
                                             ($record['status'] === 'Present' ? 'badge-warning' : 'badge-danger');
                                    ?>">
                                        <?php echo htmlspecialchars($record['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>

                <!-- Print and Excel Download Buttons -->
                <div class="action-buttons">
                    <button onclick="printAttendance()" class="btn-print">
                        🖨️ Print
                    </button>
                    <button onclick="exportToExcel()" class="btn-excel">
                        📊 Download Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh page every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.title = 'Dashboard - ' + timeString + ' - NCHM Admin';
        }

        updateTime();
        setInterval(updateTime, 1000);

        // Print and Excel export functions
        function printAttendance() {
            // Create a new window for printing
            const printWindow = window.open('', '_blank');
            const today = new Date().toLocaleDateString();

            // Get the attendance table
            const table = document.querySelector('.table');
            const tableHTML = table ? table.outerHTML : '<p>No attendance records for today.</p>';

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>NCHM Attendance Report - ${today}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .logo { font-size: 24px; font-weight: bold; color: #2c5aa0; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f8f9fa; font-weight: bold; }
                        .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
                        .badge-success { background: #d4edda; color: #155724; }
                        .badge-warning { background: #fff3cd; color: #856404; }
                        .badge-danger { background: #f8d7da; color: #721c24; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="logo">NCHM</div>
                        <h2>National Center for Hydrology and Meteorology</h2>
                        <h3>Daily Attendance Report</h3>
                        <p>Date: ${today}</p>
                    </div>
                    ${tableHTML}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        function exportToExcel() {
            // Get today's date for filename
            const today = new Date().toISOString().split('T')[0];

            // Create Excel export URL
            const exportUrl = 'export_attendance.php?date=' + today + '&format=excel';

            // Create a temporary link and click it to download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = `NCHM_Attendance_${today}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
