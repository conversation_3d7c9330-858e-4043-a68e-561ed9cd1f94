<?php
// Simple PHP test without any dependencies
echo "<!DOCTYPE html><html><head><title>Simple Test</title></head><body>";
echo "<h1>PHP Test</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";

// Test basic PHP functions
echo "<h2>Basic Tests</h2>";
echo "<p>JSON extension: " . (function_exists('json_encode') ? 'OK' : 'Missing') . "</p>";
echo "<p>PDO extension: " . (class_exists('PDO') ? 'OK' : 'Missing') . "</p>";
echo "<p>GD extension: " . (extension_loaded('gd') ? 'OK' : 'Missing') . "</p>";

// Test file system
echo "<h2>File System</h2>";
echo "<p>Current directory: " . __DIR__ . "</p>";
echo "<p>Can write to current dir: " . (is_writable(__DIR__) ? 'Yes' : 'No') . "</p>";

// Test database connection (simple)
echo "<h2>Database Test</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p>MySQL connection: OK</p>";
    
    // Test if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'attendance_db'");
    if ($stmt->rowCount() > 0) {
        echo "<p>attendance_db database: Found</p>";
    } else {
        echo "<p>attendance_db database: Not found</p>";
    }
} catch (Exception $e) {
    echo "<p>MySQL connection: Failed - " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
