<?php
session_start();
require_once '../classes/DatabaseManager.php';
require_once '../classes/EmailReporter.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

$db = new DatabaseManager();
$email_reporter = new EmailReporter();

$message = '';
$error = '';

// Handle report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['generate_daily_report'])) {
        $report_date = $_POST['report_date'] ?? date('Y-m-d');
        
        try {
            $report_data = $email_reporter->generateDailyReport($report_date);
            $_SESSION['report_data'] = $report_data;
            $message = "Report generated successfully for " . date('F j, Y', strtotime($report_date));
        } catch (Exception $e) {
            $error = "Error generating report: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['send_email_report'])) {
        $report_date = $_POST['report_date'] ?? date('Y-m-d');
        
        try {
            $result = $email_reporter->sendDailyReport($report_date);
            if ($result['success']) {
                $message = "Email report sent successfully for " . date('F j, Y', strtotime($report_date));
            } else {
                $error = $result['message'];
            }
        } catch (Exception $e) {
            $error = "Error sending email: " . $e->getMessage();
        }
    }
}

// Handle Excel export
if (isset($_GET['export']) && $_GET['export'] === 'excel' && isset($_GET['date'])) {
    $export_date = $_GET['date'];
    $report_data = $email_reporter->generateDailyReport($export_date);
    
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="attendance_report_' . $export_date . '.xls"');
    header('Cache-Control: max-age=0');
    
    echo "<table border='1'>";
    echo "<tr><th colspan='8' style='text-align: center; font-size: 16px; font-weight: bold;'>Daily Attendance Report - " . date('F j, Y', strtotime($export_date)) . "</th></tr>";
    echo "<tr><th>Name</th><th>EID/CID/Permit</th><th>Position</th><th>Division</th><th>Clock In</th><th>Clock Out</th><th>Total Hours</th><th>Status</th></tr>";
    
    foreach ($report_data['attendance'] as $staff) {
        $clock_in = $staff['clock_in'] ? date('H:i:s', strtotime($staff['clock_in'])) : '-';
        $clock_out = $staff['clock_out'] ? date('H:i:s', strtotime($staff['clock_out'])) : '-';
        $total_hours = $staff['total_hours'] > 0 ? $staff['total_hours'] . ' hrs' : '-';
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($staff['name']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['eid_cid_permit']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['position_title']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['division']) . "</td>";
        echo "<td>$clock_in</td>";
        echo "<td>$clock_out</td>";
        echo "<td>$total_hours</td>";
        echo "<td>" . $staff['status'] . "</td>";
        echo "</tr>";
    }
    
    echo "<tr><td colspan='8'></td></tr>";
    echo "<tr><td><strong>Summary:</strong></td><td colspan='7'></td></tr>";
    echo "<tr><td>Total Staff:</td><td>" . $report_data['summary']['total_staff'] . "</td><td colspan='6'></td></tr>";
    echo "<tr><td>Present:</td><td>" . $report_data['summary']['present'] . "</td><td colspan='6'></td></tr>";
    echo "<tr><td>Absent:</td><td>" . $report_data['summary']['absent'] . "</td><td colspan='6'></td></tr>";
    echo "</table>";
    exit;
}

$page_title = 'Reports';
$report_data = $_SESSION['report_data'] ?? null;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - DHM Admin</title>
    <link rel="stylesheet" href="../assets/admin-style.css">
    <style>
        .report-controls {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 1rem;
            align-items: end;
        }

        .report-summary {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid var(--water-blue);
        }

        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--water-blue);
        }

        .report-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .status-present {
            color: var(--success-green);
            font-weight: bold;
        }

        .status-absent {
            color: var(--error-red);
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }

            .report-summary {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    <?php include 'includes/navigation.php'; ?>

    <div class="main-content">
        <div class="page-header">
            <h1><?php echo $page_title; ?></h1>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <div class="report-controls">
            <h3>Generate Attendance Report</h3>
            <form method="POST">
                <div class="controls-grid">
                    <div class="form-group">
                        <label for="report_date">Report Date:</label>
                        <input type="date" id="report_date" name="report_date" class="form-control" 
                               value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>">
                    </div>
                    <button type="submit" name="generate_daily_report" class="btn">Generate Report</button>
                    <button type="submit" name="send_email_report" class="btn btn-success">Send Email</button>
                </div>
            </form>
        </div>

        <?php if ($report_data): ?>
        <div class="report-summary">
            <div class="summary-card">
                <div class="summary-number"><?php echo $report_data['summary']['total_staff']; ?></div>
                <div>Total Staff</div>
            </div>
            <div class="summary-card">
                <div class="summary-number"><?php echo $report_data['summary']['present']; ?></div>
                <div>Present</div>
            </div>
            <div class="summary-card">
                <div class="summary-number"><?php echo $report_data['summary']['absent']; ?></div>
                <div>Absent</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Attendance Report - <?php echo date('F j, Y', strtotime($report_data['date'])); ?></h3>
                <div>
                    <button onclick="window.print()" class="btn">🖨️ Print</button>
                    <a href="?export=excel&date=<?php echo $report_data['date']; ?>" class="btn btn-success">📊 Export Excel</a>
                </div>
            </div>
            <div class="card-content">
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>EID/CID/Permit</th>
                                <th>Position</th>
                                <th>Division</th>
                                <th>Clock In</th>
                                <th>Clock Out</th>
                                <th>Total Hours</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($report_data['attendance'] as $staff): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($staff['name']); ?></td>
                                <td><?php echo htmlspecialchars($staff['eid_cid_permit']); ?></td>
                                <td><?php echo htmlspecialchars($staff['position_title']); ?></td>
                                <td><?php echo htmlspecialchars($staff['division']); ?></td>
                                <td><?php echo $staff['clock_in'] ? date('H:i:s', strtotime($staff['clock_in'])) : '-'; ?></td>
                                <td><?php echo $staff['clock_out'] ? date('H:i:s', strtotime($staff['clock_out'])) : '-'; ?></td>
                                <td><?php echo $staff['total_hours'] > 0 ? $staff['total_hours'] . ' hrs' : '-'; ?></td>
                                <td class="status-<?php echo strtolower($staff['status']); ?>">
                                    <?php echo $staff['status']; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Email Report History</h3>
            </div>
            <div class="card-content">
                <?php
                $email_history = $db->conn->query(
                    "SELECT * FROM email_reports ORDER BY sent_at DESC LIMIT 10"
                )->fetchAll(PDO::FETCH_ASSOC);
                ?>
                
                <?php if (empty($email_history)): ?>
                    <p>No email reports sent yet.</p>
                <?php else: ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Recipients</th>
                                <th>Status</th>
                                <th>Sent At</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($email_history as $report): ?>
                            <tr>
                                <td><?php echo date('F j, Y', strtotime($report['report_date'])); ?></td>
                                <td><?php echo ucfirst(str_replace('_', ' ', $report['report_type'])); ?></td>
                                <td><?php echo count(json_decode($report['recipients'])); ?> recipients</td>
                                <td>
                                    <span class="badge badge-<?php echo $report['status'] === 'sent' ? 'success' : 'danger'; ?>">
                                        <?php echo ucfirst($report['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y H:i', strtotime($report['sent_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Auto-set today's date
        document.getElementById('report_date').valueAsDate = new Date();
    </script>
</body>
</html>
