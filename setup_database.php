<?php
echo "<h2>Database Setup</h2>";

try {
    // Connect to MySQL without specifying database
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to MySQL<br>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS attendance_db");
    echo "✅ Database 'attendance_db' created/verified<br>";
    
    // Connect to the attendance_db
    $pdo = new PDO("mysql:host=localhost;dbname=attendance_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if staff table exists
    $result = $pdo->query("SHOW TABLES LIKE 'staff'");
    if ($result->rowCount() == 0) {
        echo "❌ Staff table not found. Please import attendance_db.sql first<br>";
        echo "<p>To import the database:</p>";
        echo "<ol>";
        echo "<li>Open phpMyAdmin (usually at http://localhost/phpmyadmin)</li>";
        echo "<li>Create database 'attendance_db' if it doesn't exist</li>";
        echo "<li>Select the database</li>";
        echo "<li>Click 'Import' tab</li>";
        echo "<li>Choose the 'attendance_db.sql' file</li>";
        echo "<li>Click 'Go' to import</li>";
        echo "</ol>";
    } else {
        echo "✅ Staff table found<br>";
        
        // Count staff records
        $stmt = $pdo->query("SELECT COUNT(*) FROM staff");
        $count = $stmt->fetchColumn();
        echo "✅ Found $count staff records<br>";
        
        // Check if enhanced tables exist
        $enhanced_tables = ['device_registrations', 'qr_codes', 'admin_users', 'system_logs', 'email_reports'];
        $missing_tables = [];
        
        foreach ($enhanced_tables as $table) {
            $result = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($result->rowCount() == 0) {
                $missing_tables[] = $table;
            }
        }
        
        if (!empty($missing_tables)) {
            echo "⚠️ Missing enhanced tables: " . implode(', ', $missing_tables) . "<br>";
            echo "<p>Creating missing tables...</p>";
            
            // Create missing tables
            $sql_commands = [
                "CREATE TABLE IF NOT EXISTS `device_registrations` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `staff_id` int NOT NULL,
                  `registration_id` varchar(255) NOT NULL UNIQUE,
                  `device_token` varchar(255) NOT NULL UNIQUE,
                  `device_model` varchar(100) NOT NULL,
                  `device_fingerprint` text,
                  `ip_address` varchar(45),
                  `user_agent` text,
                  `registration_date` datetime DEFAULT CURRENT_TIMESTAMP,
                  `is_active` tinyint(1) DEFAULT 1,
                  `last_used` datetime DEFAULT NULL,
                  PRIMARY KEY (`id`),
                  FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci",
                
                "CREATE TABLE IF NOT EXISTS `qr_codes` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `qr_code` varchar(255) NOT NULL UNIQUE,
                  `qr_type` enum('registration','daily_attendance') NOT NULL,
                  `staff_id` int DEFAULT NULL,
                  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
                  `expiry_date` datetime NOT NULL,
                  `is_active` tinyint(1) DEFAULT 1,
                  `usage_count` int DEFAULT 0,
                  `max_usage` int DEFAULT NULL,
                  PRIMARY KEY (`id`),
                  FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci",
                
                "CREATE TABLE IF NOT EXISTS `admin_users` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `username` varchar(50) NOT NULL UNIQUE,
                  `password_hash` varchar(255) NOT NULL,
                  `full_name` varchar(100) NOT NULL,
                  `email` varchar(100) NOT NULL,
                  `role` enum('admin','super_admin') NOT NULL DEFAULT 'admin',
                  `is_active` tinyint(1) DEFAULT 1,
                  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
                  `last_login` datetime DEFAULT NULL,
                  `login_attempts` int DEFAULT 0,
                  `locked_until` datetime DEFAULT NULL,
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci",
                
                "CREATE TABLE IF NOT EXISTS `system_logs` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `log_type` enum('login','logout','registration','attendance','admin_action','error','security') NOT NULL,
                  `user_id` int DEFAULT NULL,
                  `staff_id` int DEFAULT NULL,
                  `admin_id` int DEFAULT NULL,
                  `action` varchar(255) NOT NULL,
                  `details` text,
                  `ip_address` varchar(45),
                  `user_agent` text,
                  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci",
                
                "CREATE TABLE IF NOT EXISTS `email_reports` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `report_date` date NOT NULL,
                  `report_type` varchar(50) NOT NULL,
                  `recipients` text NOT NULL,
                  `subject` varchar(255) NOT NULL,
                  `sent_at` datetime DEFAULT CURRENT_TIMESTAMP,
                  `status` enum('sent','failed','pending') DEFAULT 'pending',
                  `error_message` text DEFAULT NULL,
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
            ];
            
            foreach ($sql_commands as $sql) {
                try {
                    $pdo->exec($sql);
                    echo "✅ Table created successfully<br>";
                } catch (Exception $e) {
                    echo "❌ Error creating table: " . $e->getMessage() . "<br>";
                }
            }
            
            // Add missing columns to staff table
            try {
                $pdo->exec("ALTER TABLE `staff` ADD COLUMN IF NOT EXISTS `registration_id` varchar(255) DEFAULT NULL UNIQUE AFTER `device_model`");
                $pdo->exec("ALTER TABLE `staff` ADD COLUMN IF NOT EXISTS `is_registered` tinyint(1) DEFAULT 0 AFTER `registration_id`");
                $pdo->exec("ALTER TABLE `staff` ADD COLUMN IF NOT EXISTS `registration_qr_code` varchar(255) DEFAULT NULL AFTER `is_registered`");
                echo "✅ Staff table columns added<br>";
            } catch (Exception $e) {
                echo "⚠️ Staff table columns may already exist<br>";
            }
            
            // Create default admin user
            try {
                $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT IGNORE INTO `admin_users` (`username`, `password_hash`, `full_name`, `email`, `role`) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute(['superadmin', $password_hash, 'Super Administrator', '<EMAIL>', 'super_admin']);
                echo "✅ Default admin user created (username: superadmin, password: admin123)<br>";
            } catch (Exception $e) {
                echo "⚠️ Admin user may already exist<br>";
            }
            
        } else {
            echo "✅ All enhanced tables found<br>";
        }
    }
    
    echo "<br><h3>Setup Complete!</h3>";
    echo "<p>You can now access:</p>";
    echo "<ul>";
    echo "<li><a href='index.php'>Main Page</a></li>";
    echo "<li><a href='admin/login.php'>Admin Login</a> (username: superadmin, password: admin123)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    echo "<p>Please check your database configuration and ensure MySQL is running.</p>";
}
?>
