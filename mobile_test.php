<?php
// Simple Mobile-Friendly Network Test for NCHM Attendance System
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
$db = new Database();

$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
$server_ip = $_SERVER['SERVER_ADDR'] ?? 'Unknown';
$host = $_SERVER['HTTP_HOST'] ?? 'Unknown';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

// Check if this is a mobile device
$is_mobile = (strpos($user_agent, 'Mobile') !== false || 
              strpos($user_agent, 'Android') !== false || 
              strpos($user_agent, 'iPhone') !== false);

// Check if IP is in allowed range
$ip_allowed = $db->validateIPRange($client_ip);

// Check if accessing via network IP
$network_access = (strpos($host, '*************') !== false);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCHM Attendance - Mobile Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }
        .status-card {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 5px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #3498db;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
        }
        .test-btn:hover {
            background: #2980b9;
        }
        .success-btn {
            background: #28a745;
        }
        .success-btn:hover {
            background: #218838;
        }
        .details {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        .big-status {
            font-size: 24px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 NCHM Attendance</h1>
            <h2>Mobile Network Test</h2>
            <p><?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- Overall Status -->
        <?php if ($network_access && $ip_allowed && $is_mobile): ?>
            <div class="big-status success">
                ✅ ALL SYSTEMS GO!<br>
                <small>Your device should work with the attendance system</small>
            </div>
        <?php else: ?>
            <div class="big-status error">
                ⚠️ ISSUES DETECTED<br>
                <small>Please check the details below</small>
            </div>
        <?php endif; ?>

        <!-- Device Type Check -->
        <div class="status-card <?php echo $is_mobile ? 'success' : 'warning'; ?>">
            <strong>📱 Device Type:</strong>
            <?php if ($is_mobile): ?>
                ✅ Mobile Device Detected
            <?php else: ?>
                ⚠️ Desktop/Laptop Detected
            <?php endif; ?>
            <div class="details">User Agent: <?php echo substr($user_agent, 0, 50) . '...'; ?></div>
        </div>

        <!-- Network Access Check -->
        <div class="status-card <?php echo $network_access ? 'success' : 'error'; ?>">
            <strong>🌐 Network Access:</strong>
            <?php if ($network_access): ?>
                ✅ Accessing via Network IP (*************)
            <?php else: ?>
                ❌ Not accessing via network IP
            <?php endif; ?>
            <div class="details">Host: <?php echo $host; ?></div>
        </div>

        <!-- IP Range Check -->
        <div class="status-card <?php echo $ip_allowed ? 'success' : 'error'; ?>">
            <strong>🔒 IP Authorization:</strong>
            <?php if ($ip_allowed): ?>
                ✅ Your IP is authorized (<?php echo $client_ip; ?>)
            <?php else: ?>
                ❌ Your IP is not in allowed range (<?php echo $client_ip; ?>)
            <?php endif; ?>
            <div class="details">
                Allowed ranges: ***********-255, *************, localhost
            </div>
        </div>

        <!-- Database Connection Check -->
        <?php
        try {
            $conn = $db->getConnection();
            $db_ok = ($conn !== null);
        } catch (Exception $e) {
            $db_ok = false;
        }
        ?>
        <div class="status-card <?php echo $db_ok ? 'success' : 'error'; ?>">
            <strong>💾 Database:</strong>
            <?php echo $db_ok ? '✅ Connected' : '❌ Connection Failed'; ?>
        </div>

        <!-- Action Buttons -->
        <div style="margin-top: 30px;">
            <?php if ($network_access && $ip_allowed): ?>
                <a href="index.php" class="test-btn success-btn">
                    🎯 Go to Attendance System
                </a>
            <?php else: ?>
                <div class="status-card error">
                    <strong>❌ Cannot Access Attendance System</strong><br>
                    Please fix the issues above first.
                </div>
            <?php endif; ?>
            
            <a href="generate_qr.php?type=attendance" class="test-btn">
                📱 Test QR Code Generation
            </a>
            
            <a href="network_diagnostic.php" class="test-btn">
                🔧 Full Diagnostic Report
            </a>
        </div>

        <!-- Instructions for Staff -->
        <div class="status-card info">
            <strong>📋 Instructions for Staff:</strong>
            <ol style="margin: 10px 0; padding-left: 20px;">
                <li>Connect to office Wi-Fi</li>
                <li>Open browser and go to: <strong>http://*************:8080/attendance/</strong></li>
                <li>If this page shows "ALL SYSTEMS GO", the attendance system should work</li>
                <li>If you see errors, contact IT support</li>
            </ol>
        </div>

        <!-- Troubleshooting -->
        <?php if (!$network_access || !$ip_allowed): ?>
        <div class="status-card warning">
            <strong>🔧 Quick Fixes:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <?php if (!$network_access): ?>
                <li>Make sure you're using: <strong>http://*************:8080/attendance/</strong></li>
                <li>Don't use localhost or 127.0.0.1</li>
                <?php endif; ?>
                
                <?php if (!$ip_allowed): ?>
                <li>Ensure you're connected to office Wi-Fi (172.31.18.x network)</li>
                <li>Try disconnecting and reconnecting to Wi-Fi</li>
                <li>Contact IT if your IP range needs to be added</li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>

        <!-- Technical Details -->
        <div class="status-card info">
            <strong>🔍 Technical Details:</strong>
            <div class="details">
                <strong>Your IP:</strong> <?php echo $client_ip; ?><br>
                <strong>Server IP:</strong> <?php echo $server_ip; ?><br>
                <strong>Access URL:</strong> <?php echo $host; ?><br>
                <strong>Time:</strong> <?php echo date('Y-m-d H:i:s T'); ?>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh every 10 seconds if there are issues
        <?php if (!$network_access || !$ip_allowed): ?>
        setTimeout(() => {
            if (confirm('Refresh to check if issues are resolved?')) {
                location.reload();
            }
        }, 10000);
        <?php endif; ?>
    </script>
</body>
</html>
