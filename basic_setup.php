<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>DHM Attendance Setup</title></head><body>";
echo "<h1>DHM Attendance System Setup</h1>";

// Step 1: Check PHP
echo "<h2>Step 1: PHP Check</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
if (version_compare(PHP_VERSION, '7.4.0') >= 0) {
    echo "✅ PHP version is compatible<br>";
} else {
    echo "❌ PHP version too old. Need 7.4+<br>";
}

// Step 2: Check Extensions
echo "<h2>Step 2: Extensions Check</h2>";
$extensions = ['pdo', 'pdo_mysql', 'gd', 'json'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext extension loaded<br>";
    } else {
        echo "❌ $ext extension missing<br>";
    }
}

// Step 3: Database Connection
echo "<h2>Step 3: Database Setup</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to MySQL<br>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS attendance_db");
    echo "✅ Database 'attendance_db' ready<br>";
    
    // Connect to attendance_db
    $pdo = new PDO("mysql:host=localhost;dbname=attendance_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if staff table exists
    $result = $pdo->query("SHOW TABLES LIKE 'staff'");
    if ($result->rowCount() == 0) {
        echo "⚠️ Staff table not found. Creating basic structure...<br>";
        
        // Create basic staff table
        $sql = "CREATE TABLE IF NOT EXISTS `staff` (
            `id` int NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `eid_cid_permit` varchar(50) NOT NULL,
            `id_type` enum('CID','Permit','EID') NOT NULL,
            `position_title` varchar(100) DEFAULT NULL,
            `division` varchar(100) DEFAULT NULL,
            `device_token` varchar(255) DEFAULT NULL,
            `device_model` varchar(100) DEFAULT NULL,
            `registered_date` datetime DEFAULT CURRENT_TIMESTAMP,
            `registration_id` varchar(255) DEFAULT NULL,
            `is_registered` tinyint(1) DEFAULT 0,
            `registration_qr_code` varchar(255) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `eid_cid_permit` (`eid_cid_permit`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
        
        $pdo->exec($sql);
        echo "✅ Staff table created<br>";
        
        // Insert a test staff member
        $stmt = $pdo->prepare("INSERT IGNORE INTO staff (name, eid_cid_permit, id_type, position_title, division) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['Test User', 'TEST001', 'EID', 'Test Position', 'SECT']);
        echo "✅ Test staff member added<br>";
        
    } else {
        echo "✅ Staff table exists<br>";
        $stmt = $pdo->query("SELECT COUNT(*) FROM staff");
        $count = $stmt->fetchColumn();
        echo "✅ Found $count staff records<br>";
    }
    
    // Create attendance_logs table
    $sql = "CREATE TABLE IF NOT EXISTS `attendance_logs` (
        `id` int NOT NULL AUTO_INCREMENT,
        `staff_id` int NOT NULL,
        `action` enum('clock_in','clock_out') NOT NULL,
        `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `staff_id` (`staff_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    $pdo->exec($sql);
    echo "✅ Attendance logs table ready<br>";
    
    // Create admin_users table
    $sql = "CREATE TABLE IF NOT EXISTS `admin_users` (
        `id` int NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `password_hash` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `role` enum('admin','super_admin') NOT NULL DEFAULT 'admin',
        `is_active` tinyint(1) DEFAULT 1,
        `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
        `last_login` datetime DEFAULT NULL,
        `login_attempts` int DEFAULT 0,
        `locked_until` datetime DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    $pdo->exec($sql);
    echo "✅ Admin users table ready<br>";
    
    // Create default admin
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, password_hash, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['superadmin', $password_hash, 'Super Administrator', '<EMAIL>', 'super_admin']);
    echo "✅ Default admin user ready<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Step 4: Create directories
echo "<h2>Step 4: Directory Setup</h2>";
$dirs = ['qr_codes', 'logs'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Created directory: $dir<br>";
        } else {
            echo "❌ Failed to create directory: $dir<br>";
        }
    } else {
        echo "✅ Directory exists: $dir<br>";
    }
    
    if (is_writable($dir)) {
        echo "✅ Directory writable: $dir<br>";
    } else {
        echo "❌ Directory not writable: $dir<br>";
    }
}

echo "<h2>Setup Complete!</h2>";
echo "<p><strong>Default Admin Credentials:</strong></p>";
echo "<ul>";
echo "<li>Username: <code>superadmin</code></li>";
echo "<li>Password: <code>admin123</code></li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='simple_test.php'>Run Simple Test</a></li>";
echo "<li><a href='index.php'>Visit Main Page</a></li>";
echo "<li><a href='admin/login.php'>Admin Login</a></li>";
echo "</ol>";

echo "</body></html>";
?>
