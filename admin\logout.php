<?php
session_start();
require_once '../classes/DatabaseManager.php';

// Log logout action if admin was logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    $db = new DatabaseManager();
    $db->logAction('logout', 'Admin logout', 
        json_encode(['username' => $_SESSION['admin_username']]),
        null, $_SESSION['admin_id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
}

// Destroy session
session_destroy();

// Redirect to login page
header('Location: login.php?logged_out=1');
exit;
?>
