<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/DatabaseManager.php';

class SecurityValidator {
    private $db;
    private $allowed_ip_ranges;
    private $max_attempts_per_hour = 10;
    private $max_devices_per_staff = 1;

    public function __construct() {
        $this->db = new DatabaseManager();
        $this->allowed_ip_ranges = [
            SystemConfig::SERVER_IP,
            SystemConfig::OFFICE_WIFI_RANGE
        ];
    }

    /**
     * Validate IP address against allowed ranges
     */
    public function validateIPAddress($ip) {
        // Allow localhost for testing
        if ($ip === '127.0.0.1' || $ip === '::1') {
            return true;
        }

        foreach ($this->allowed_ip_ranges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        // Log unauthorized IP access attempt
        $this->db->logAction('security', 'Unauthorized IP access attempt', 
            json_encode(['ip' => $ip, 'allowed_ranges' => $this->allowed_ip_ranges]),
            null, null, $ip, $_SERVER['HTTP_USER_AGENT'] ?? '');

        return false;
    }

    /**
     * Check if IP is in range
     */
    private function ipInRange($ip, $range) {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $bits) = explode('/', $range);
        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet_long &= $mask;
        
        return ($ip_long & $mask) === $subnet_long;
    }

    /**
     * Validate device fingerprint and detect potential spoofing
     */
    public function validateDeviceFingerprint($registration_id, $current_fingerprint, $ip, $user_agent) {
        // Get stored device registration
        $stmt = $this->db->conn->prepare(
            "SELECT * FROM device_registrations WHERE registration_id = ? AND is_active = 1"
        );
        $stmt->execute([$registration_id]);
        $device_reg = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$device_reg) {
            return ['valid' => false, 'reason' => 'Device not registered'];
        }

        $stored_fingerprint = $device_reg['device_fingerprint'];
        $stored_user_agent = $device_reg['user_agent'];

        // Exact fingerprint match
        if ($stored_fingerprint === $current_fingerprint) {
            return ['valid' => true, 'confidence' => 100];
        }

        // Analyze fingerprint similarity
        $similarity = $this->calculateFingerprintSimilarity($stored_fingerprint, $current_fingerprint);
        
        // Check user agent similarity
        $ua_similarity = $this->calculateUserAgentSimilarity($stored_user_agent, $user_agent);

        // Combined confidence score
        $confidence = ($similarity * 0.7) + ($ua_similarity * 0.3);

        // Log suspicious activity if confidence is low
        if ($confidence < 70) {
            $this->db->logAction('security', 'Suspicious device fingerprint', 
                json_encode([
                    'registration_id' => $registration_id,
                    'confidence' => $confidence,
                    'fingerprint_similarity' => $similarity,
                    'ua_similarity' => $ua_similarity
                ]),
                null, null, $ip, $user_agent);
        }

        return [
            'valid' => $confidence >= 70,
            'confidence' => $confidence,
            'reason' => $confidence < 70 ? 'Device fingerprint mismatch' : 'Valid'
        ];
    }

    /**
     * Calculate fingerprint similarity percentage
     */
    private function calculateFingerprintSimilarity($stored, $current) {
        if (empty($stored) || empty($current)) return 0;
        
        $stored_parts = explode('|', base64_decode($stored));
        $current_parts = explode('|', base64_decode($current));
        
        if (count($stored_parts) !== count($current_parts)) return 0;
        
        $matches = 0;
        $total = count($stored_parts);
        
        for ($i = 0; $i < $total; $i++) {
            if (isset($stored_parts[$i]) && isset($current_parts[$i])) {
                if ($stored_parts[$i] === $current_parts[$i]) {
                    $matches++;
                } elseif ($i === 0) { // User agent - allow partial match
                    $similarity = similar_text($stored_parts[$i], $current_parts[$i]);
                    if ($similarity > strlen($stored_parts[$i]) * 0.8) {
                        $matches += 0.8;
                    }
                }
            }
        }
        
        return ($matches / $total) * 100;
    }

    /**
     * Calculate user agent similarity
     */
    private function calculateUserAgentSimilarity($stored, $current) {
        if (empty($stored) || empty($current)) return 0;
        
        $similarity = 0;
        similar_text($stored, $current, $similarity);
        return $similarity;
    }

    /**
     * Check for rate limiting - prevent brute force attacks
     */
    public function checkRateLimit($ip, $action = 'attendance') {
        $hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        
        $stmt = $this->db->conn->prepare(
            "SELECT COUNT(*) FROM system_logs WHERE ip_address = ? AND action LIKE ? AND timestamp > ?"
        );
        $stmt->execute([$ip, "%$action%", $hour_ago]);
        $attempts = $stmt->fetchColumn();

        if ($attempts >= $this->max_attempts_per_hour) {
            // Log rate limit exceeded
            $this->db->logAction('security', 'Rate limit exceeded', 
                json_encode(['ip' => $ip, 'action' => $action, 'attempts' => $attempts]),
                null, null, $ip, $_SERVER['HTTP_USER_AGENT'] ?? '');
            
            return false;
        }

        return true;
    }

    /**
     * Detect potential proxy or VPN usage
     */
    public function detectProxyUsage($ip) {
        // Check for common proxy headers
        $proxy_headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'HTTP_VIA'
        ];

        $proxy_detected = false;
        $proxy_info = [];

        foreach ($proxy_headers as $header) {
            if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
                $proxy_detected = true;
                $proxy_info[$header] = $_SERVER[$header];
            }
        }

        // Check for suspicious IP patterns
        if ($this->isSuspiciousIP($ip)) {
            $proxy_detected = true;
            $proxy_info['suspicious_ip'] = true;
        }

        if ($proxy_detected) {
            $this->db->logAction('security', 'Potential proxy/VPN detected', 
                json_encode(['ip' => $ip, 'proxy_info' => $proxy_info]),
                null, null, $ip, $_SERVER['HTTP_USER_AGENT'] ?? '');
        }

        return $proxy_detected;
    }

    /**
     * Check if IP appears suspicious
     */
    private function isSuspiciousIP($ip) {
        // Check for common VPN/proxy IP ranges (simplified)
        $suspicious_ranges = [
            '10.0.0.0/8',      // Private network (if coming from outside)
            '***********/16',  // Private network (if coming from outside)
            '***********/16'   // Link-local
        ];

        // Only flag as suspicious if it's a private IP coming from outside our network
        if (!$this->ipInRange($ip, SystemConfig::OFFICE_WIFI_RANGE) && 
            !$ip === SystemConfig::SERVER_IP) {
            foreach ($suspicious_ranges as $range) {
                if ($this->ipInRange($ip, $range)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Validate device uniqueness per staff member
     */
    public function validateDeviceUniqueness($staff_id, $device_token, $exclude_registration_id = null) {
        $query = "SELECT COUNT(*) FROM device_registrations 
                  WHERE staff_id = ? AND device_token != ? AND is_active = 1";
        $params = [$staff_id, $device_token];
        
        if ($exclude_registration_id) {
            $query .= " AND registration_id != ?";
            $params[] = $exclude_registration_id;
        }
        
        $stmt = $this->db->conn->prepare($query);
        $stmt->execute($params);
        $existing_devices = $stmt->fetchColumn();

        if ($existing_devices >= $this->max_devices_per_staff) {
            $this->db->logAction('security', 'Multiple device registration attempt', 
                json_encode(['staff_id' => $staff_id, 'existing_devices' => $existing_devices]),
                $staff_id, null, $_SERVER['REMOTE_ADDR'] ?? '', $_SERVER['HTTP_USER_AGENT'] ?? '');
            
            return false;
        }

        return true;
    }

    /**
     * Check for suspicious attendance patterns
     */
    public function detectSuspiciousAttendancePattern($staff_id, $action) {
        // Get last 5 attendance records for this staff
        $stmt = $this->db->conn->prepare(
            "SELECT action, timestamp FROM attendance_logs 
             WHERE staff_id = ? ORDER BY timestamp DESC LIMIT 5"
        );
        $stmt->execute([$staff_id]);
        $recent_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $suspicious_patterns = [];

        // Check for rapid consecutive actions
        if (count($recent_records) >= 2) {
            $last_record = $recent_records[0];
            $time_diff = time() - strtotime($last_record['timestamp']);
            
            // Same action within 5 minutes
            if ($last_record['action'] === $action && $time_diff < 300) {
                $suspicious_patterns[] = 'Duplicate action within 5 minutes';
            }
            
            // Too many actions in short time
            $actions_last_hour = 0;
            foreach ($recent_records as $record) {
                if (time() - strtotime($record['timestamp']) < 3600) {
                    $actions_last_hour++;
                }
            }
            
            if ($actions_last_hour > 4) {
                $suspicious_patterns[] = 'Too many actions in one hour';
            }
        }

        if (!empty($suspicious_patterns)) {
            $this->db->logAction('security', 'Suspicious attendance pattern', 
                json_encode(['staff_id' => $staff_id, 'patterns' => $suspicious_patterns]),
                $staff_id, null, $_SERVER['REMOTE_ADDR'] ?? '', $_SERVER['HTTP_USER_AGENT'] ?? '');
            
            return false;
        }

        return true;
    }

    /**
     * Generate security report
     */
    public function generateSecurityReport($days = 7) {
        $since_date = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $stmt = $this->db->conn->prepare(
            "SELECT log_type, action, COUNT(*) as count, 
             DATE(timestamp) as date
             FROM system_logs 
             WHERE log_type = 'security' AND timestamp > ?
             GROUP BY log_type, action, DATE(timestamp)
             ORDER BY timestamp DESC"
        );
        $stmt->execute([$since_date]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get blocked IPs
     */
    public function getBlockedIPs($hours = 24) {
        $since_time = date('Y-m-d H:i:s', strtotime("-$hours hours"));
        
        $stmt = $this->db->conn->prepare(
            "SELECT ip_address, COUNT(*) as violations, MAX(timestamp) as last_violation
             FROM system_logs 
             WHERE log_type = 'security' AND timestamp > ?
             GROUP BY ip_address
             HAVING violations >= 3
             ORDER BY violations DESC"
        );
        $stmt->execute([$since_time]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
