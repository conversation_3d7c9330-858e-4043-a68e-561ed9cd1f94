<?php
// Mobile Device Registration Page
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

require_once 'classes/DatabaseManager.php';
require_once 'classes/QRCodeManager.php';

$db = new DatabaseManager();
$qr_manager = new QRCodeManager();

$message = '';
$error = '';
$success = false;
$registration_details = [];

// Get QR code from URL
$qr_code = $_GET['code'] ?? '';

if (empty($qr_code)) {
    $error = 'Invalid registration link. Please scan the QR code again.';
} else {
    // Validate QR code
    $qr_data = $db->getQRCode($qr_code);
    if (!$qr_data || $qr_data['qr_type'] !== 'registration') {
        $error = 'Invalid or expired registration QR code.';
    }
}

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error) {
    $staff_id = $_POST['staff_id'] ?? '';
    // Extract device model from user agent
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    $device_model = $_POST['device_model'] ?? $user_agent;

    // Try to extract a shorter device model from user agent
    if ($device_model === $user_agent) {
        if (strpos($user_agent, 'iPhone') !== false) {
            preg_match('/iPhone[^;)]*/', $user_agent, $matches);
            $device_model = $matches[0] ?? 'iPhone';
        } elseif (strpos($user_agent, 'Android') !== false) {
            preg_match('/Android[^;)]*/', $user_agent, $matches);
            $device_model = $matches[0] ?? 'Android';
        } elseif (strpos($user_agent, 'Windows') !== false) {
            $device_model = 'Windows Device';
        } else {
            // Truncate long user agent to fit in database
            $device_model = substr($user_agent, 0, 450) . (strlen($user_agent) > 450 ? '...' : '');
        }
    }

    $device_info = [
        'model' => $device_model,
        'fingerprint' => md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT']
    ];
    
    if (empty($staff_id)) {
        $error = 'Please select your name and ID.';
    } else {
        try {
            // Check if staff member exists
            $staff = $db->getStaffById($staff_id);
            if (!$staff) {
                $error = 'Selected staff member not found.';
            } else {
                // Check if this staff member is already registered
                $existing_registration = $db->checkStaffAlreadyRegistered($staff['name'], $staff['eid_cid_permit']);
                if ($existing_registration) {
                    $error = 'already_registered'; // Special error code for popup
                } else {
                    // Generate unique registration ID and device token
                    $registration_id = 'REG_' . $staff_id . '_' . date('Ymd_His');
                    $device_token = hash('sha256', $staff_id . $device_info['fingerprint'] . time());

                    // Register device
                    $db->createDeviceRegistration(
                        $staff_id,
                        $registration_id,
                        $device_token,
                        $device_info['model'],
                        $device_info['fingerprint'],
                        $device_info['ip'],
                        $device_info['user_agent']
                    );

                    // Update staff record
                    $db->updateStaffRegistration($staff_id, $registration_id, $device_token, $device_info['model']);

                    // Mark QR code as used
                    $db->incrementQRUsage($qr_code);

                    $success = true;
                    $message = "Thank you for registering!";
                    $registration_details = [
                        'staff_name' => $staff['name'],
                        'registration_id' => $registration_id,
                        'device_token' => substr($device_token, 0, 8) . '...'
                    ];
                    // Store full device token for localStorage
                    $full_device_token = $device_token;
                }
            }
        } catch (Exception $e) {
            $error = 'Registration failed: ' . $e->getMessage();
        }
    }
}

// Get all staff for dropdown
$staff_list = [];
if (!$error) {
    try {
        $staff_list = $db->getAllStaff();
    } catch (Exception $e) {
        $error = 'Unable to load staff list: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Device Registration - NCHM Attendance</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px 20px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            background: white;
            color: #2c3e50;
        }

        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .register-btn {
            width: 100%;
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
            padding: 18px;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .register-btn:hover {
            transform: translateY(-2px);
        }

        .register-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .success {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
        }

        .success h3 {
            font-size: 28px;
            margin-bottom: 15px;
        }

        .success p {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .registration-details {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            text-align: left;
        }

        .registration-details h4 {
            margin-bottom: 10px;
            font-size: 16px;
        }

        .registration-details p {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #6c757d;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
        }

        .success .back-link a {
            color: white;
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .content {
                padding: 20px 15px;
            }
        }

        /* Popup animation */
        .success {
            animation: successPop 0.6s ease-out;
        }

        @keyframes successPop {
            0% {
                transform: scale(0.8);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Device Registration</h1>
            <p>NCHM Attendance System</p>
        </div>

        <div class="content">
            <?php if ($success): ?>
                <div class="message success">
                    <h3>🎉 <?php echo htmlspecialchars($message); ?></h3>
                    <p>Your device has been successfully registered!</p>
                    
                    <div class="registration-details">
                        <h4>📋 Registration Details:</h4>
                        <p><strong>Name:</strong> <?php echo htmlspecialchars($registration_details['staff_name']); ?></p>
                        <p><strong>Registration ID:</strong> <?php echo htmlspecialchars($registration_details['registration_id']); ?></p>
                        <p><strong>Device Token:</strong> <?php echo htmlspecialchars($registration_details['device_token']); ?></p>
                    </div>
                    
                    <p style="margin-top: 15px; font-size: 16px;">You can now use this device to scan attendance QR codes.</p>
                </div>
                
                <div class="back-link">
                    <a href="index.php">← Back to Main Page</a>
                </div>
                
            <?php elseif ($error === 'already_registered'): ?>
                <script>
                    // Show popup for already registered
                    alert('⚠️ You have already registered!\n\nThis name and EID/CID/Permit combination is already registered in the system. You cannot register the same details again.');

                    // Optionally redirect after popup
                    setTimeout(function() {
                        window.location.href = 'index.php';
                    }, 1000);
                </script>
                <div class="message error">
                    <h3>⚠️ Already Registered</h3>
                    <p>You have already registered with these details.</p>
                </div>

                <div class="back-link">
                    <a href="index.php">← Back to Main Page</a>
                </div>

            <?php elseif ($error): ?>
                <div class="message error">
                    <h3>⚠️ Registration Error</h3>
                    <p><?php echo htmlspecialchars($error); ?></p>
                </div>

                <div class="back-link">
                    <a href="index.php">← Back to Main Page</a>
                </div>
                
            <?php else: ?>
                <form method="POST">
                    <div class="form-group">
                        <label for="staff_id">👤 Select Your Name & ID:</label>
                        <select name="staff_id" id="staff_id" required>
                            <option value="">-- Select Your Name --</option>
                            <?php foreach ($staff_list as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>">
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['eid_cid_permit'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="device-info">
                        <strong>📱 Device Information:</strong><br>
                        <small>IP: <?php echo $_SERVER['REMOTE_ADDR']; ?></small><br>
                        <small>Browser: <?php echo substr($_SERVER['HTTP_USER_AGENT'], 0, 50) . '...'; ?></small>
                    </div>

                    <button type="submit" class="register-btn">
                        🔐 Register This Device
                    </button>
                </form>

                <div class="back-link">
                    <a href="index.php">← Cancel Registration</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($success): ?>
    <script>
        // Auto-scroll to top and show success animation
        window.scrollTo(0, 0);

        // Check if localStorage is available (iOS Safari private mode issue)
        function isLocalStorageAvailable() {
            try {
                const test = 'localStorage_test';
                localStorage.setItem(test, test);
                localStorage.removeItem(test);
                return true;
            } catch (e) {
                return false;
            }
        }

        if (!isLocalStorageAvailable()) {
            alert('⚠️ Warning: localStorage is not available.\n\n' +
                  'This might be due to:\n' +
                  '• Private browsing mode\n' +
                  '• Browser security settings\n' +
                  '• iOS Safari restrictions\n\n' +
                  'Your registration was successful, but you may need to register again when scanning attendance QR codes.\n\n' +
                  'Please try:\n' +
                  '1. Disable private browsing\n' +
                  '2. Clear browser cache\n' +
                  '3. Try a different browser');
        } else {
            // Store registration ID and device token in localStorage for attendance system
            try {
                localStorage.setItem('attendance_registration_id', '<?php echo $registration_details['registration_id']; ?>');
                localStorage.setItem('attendance_device_token', '<?php echo isset($full_device_token) ? $full_device_token : ''; ?>');
                localStorage.setItem('attendance_device_model', '<?php echo isset($device_info['model']) ? addslashes($device_info['model']) : ''; ?>');

                // Debug: Log what we stored
                console.log('Registration data stored:');
                console.log('Registration ID:', localStorage.getItem('attendance_registration_id'));
                console.log('Device Token:', localStorage.getItem('attendance_device_token'));
                console.log('Device Model:', localStorage.getItem('attendance_device_model'));

                // Show success message with stored data confirmation
                alert('✅ Registration successful! Device data has been stored for attendance system.');
            } catch (e) {
                console.error('Error storing registration data:', e);
                alert('❌ Warning: Registration data could not be stored. You may need to register again.\n\nError: ' + e.message);
            }
        }

        // Optional: Auto-redirect after 10 seconds
        setTimeout(function() {
            if (confirm('Registration complete! Would you like to return to the main page?')) {
                window.location.href = 'index.php';
            }
        }, 5000);
    </script>
    <?php endif; ?>
</body>
</html>
