-- Enhanced database schema for QR Attendance System
-- Add these tables to your existing attendance_db

-- Table for device registrations and unique registration IDs
CREATE TABLE IF NOT EXISTS `device_registrations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `registration_id` varchar(255) NOT NULL UNIQUE,
  `device_token` varchar(255) NOT NULL UNIQUE,
  `device_model` varchar(500) NOT NULL,
  `device_fingerprint` text,
  `ip_address` varchar(45),
  `user_agent` text,
  `registration_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT 1,
  `last_used` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE,
  INDEX `idx_registration_id` (`registration_id`),
  INDEX `idx_device_token` (`device_token`),
  INDEX `idx_staff_id` (`staff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table for QR codes (both registration and daily attendance)
CREATE TABLE IF NOT EXISTS `qr_codes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `qr_code` varchar(255) NOT NULL UNIQUE,
  `qr_type` enum('registration','daily_attendance') NOT NULL,
  `staff_id` int DEFAULT NULL, -- NULL for daily attendance QR, specific staff_id for registration QR
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `expiry_date` datetime NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `usage_count` int DEFAULT 0,
  `max_usage` int DEFAULT NULL, -- NULL for unlimited, specific number for limited use
  PRIMARY KEY (`id`),
  FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE,
  INDEX `idx_qr_code` (`qr_code`),
  INDEX `idx_qr_type` (`qr_type`),
  INDEX `idx_expiry_date` (`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table for admin users
CREATE TABLE IF NOT EXISTS `admin_users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('admin','super_admin') NOT NULL DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_username` (`username`),
  INDEX `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table for system logs
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `log_type` enum('login','logout','registration','attendance','admin_action','error','security') NOT NULL,
  `user_id` int DEFAULT NULL,
  `staff_id` int DEFAULT NULL,
  `admin_id` int DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `details` text,
  `ip_address` varchar(45),
  `user_agent` text,
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL,
  INDEX `idx_log_type` (`log_type`),
  INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table for email reports
CREATE TABLE IF NOT EXISTS `email_reports` (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` varchar(50) NOT NULL,
  `recipients` text NOT NULL,
  `subject` varchar(255) NOT NULL,
  `sent_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `status` enum('sent','failed','pending') DEFAULT 'pending',
  `error_message` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_report_date` (`report_date`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Add registration_id column to staff table if not exists
ALTER TABLE `staff` 
ADD COLUMN IF NOT EXISTS `registration_id` varchar(255) DEFAULT NULL UNIQUE AFTER `device_model`,
ADD COLUMN IF NOT EXISTS `is_registered` tinyint(1) DEFAULT 0 AFTER `registration_id`,
ADD COLUMN IF NOT EXISTS `registration_qr_code` varchar(255) DEFAULT NULL AFTER `is_registered`;

-- Create default super admin user (password: admin123 - CHANGE THIS!)
INSERT IGNORE INTO `admin_users` (`username`, `password_hash`, `full_name`, `email`, `role`) 
VALUES ('superadmin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Super Administrator', '<EMAIL>', 'super_admin');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_staff_registration` ON `staff` (`registration_id`);
CREATE INDEX IF NOT EXISTS `idx_staff_registered` ON `staff` (`is_registered`);
CREATE INDEX IF NOT EXISTS `idx_attendance_timestamp` ON `attendance_logs` (`timestamp`);
CREATE INDEX IF NOT EXISTS `idx_attendance_action` ON `attendance_logs` (`action`);
