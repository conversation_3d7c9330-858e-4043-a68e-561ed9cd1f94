<?php
// Test Email Sending Functionality
// This script tests if emails can be sent to the specified addresses

echo "<h2>Testing Email Sending Functionality</h2>";

// Test email configuration
$config = require_once 'email_config.php';

echo "<h3>Email Configuration:</h3>";
echo "<p><strong>Recipients:</strong></p>";
echo "<ul>";
foreach ($config['recipients'] as $email) {
    echo "<li>" . htmlspecialchars($email) . "</li>";
}
echo "</ul>";

// Test basic email sending
$test_subject = "NCHM Attendance System - Test Email";
$test_message = "
<html>
<head>
    <title>Test Email</title>
</head>
<body>
    <h2>NCHM Attendance System Test</h2>
    <p>This is a test email to verify that the email system is working.</p>
    <p><strong>Date:</strong> " . date('Y-m-d H:i:s') . "</p>
    <p><strong>Server:</strong> " . $_SERVER['SERVER_NAME'] . "</p>
    <p>If you receive this email, the system is configured correctly.</p>
    <hr>
    <p><small>National Center for Hydrology and Meteorology<br>Royal Government of Bhutan</small></p>
</body>
</html>";

$headers = "From: NCHM Attendance System <<EMAIL>>\r\n";
$headers .= "Reply-To: <EMAIL>\r\n";
$headers .= "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: text/html; charset=UTF-8\r\n";

echo "<h3>Sending Test Emails:</h3>";

$success_count = 0;
$total_count = count($config['recipients']);

foreach ($config['recipients'] as $recipient) {
    echo "<p>Sending to: " . htmlspecialchars($recipient) . " ... ";
    
    if (mail($recipient, $test_subject, $test_message, $headers)) {
        echo "<span style='color: green;'>✅ SUCCESS</span></p>";
        $success_count++;
    } else {
        echo "<span style='color: red;'>❌ FAILED</span></p>";
    }
}

echo "<h3>Results:</h3>";
echo "<p><strong>Total Recipients:</strong> $total_count</p>";
echo "<p><strong>Successful Sends:</strong> $success_count</p>";
echo "<p><strong>Failed Sends:</strong> " . ($total_count - $success_count) . "</p>";

if ($success_count == $total_count) {
    echo "<p style='color: green; font-weight: bold;'>🎉 All emails sent successfully!</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>⚠️ Some emails failed to send.</p>";
    echo "<p><strong>Possible Issues:</strong></p>";
    echo "<ul>";
    echo "<li>Server mail configuration not set up</li>";
    echo "<li>Firewall blocking outgoing emails</li>";
    echo "<li>Need SMTP configuration instead of basic mail()</li>";
    echo "<li>Email addresses might be invalid</li>";
    echo "</ul>";
}

echo "<h3>Manual Test:</h3>";
echo "<p>You can also manually run the daily report sender:</p>";
echo "<p><code>php daily_report_sender.php</code></p>";

echo "<h3>Check Logs:</h3>";
if (file_exists('logs')) {
    echo "<p>Log files available:</p>";
    $log_files = glob('logs/*.log');
    if ($log_files) {
        echo "<ul>";
        foreach ($log_files as $log_file) {
            echo "<li><a href='$log_file' target='_blank'>" . basename($log_file) . "</a></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No log files found yet.</p>";
    }
} else {
    echo "<p>Logs directory not found. It will be created when the system runs.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
code { background-color: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
</style>
