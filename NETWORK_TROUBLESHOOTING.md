# NCHM Attendance System - Network Troubleshooting Guide

## 🚨 Problem: "This site can't be reached" from staff phones

### Quick Diagnosis
Your attendance system is working perfectly on the server (*************:8080), but staff on the 172.31.18.x network can't access it.

### ✅ What's Working
- ✅ Apache is running and listening on 0.0.0.0:8080
- ✅ Server has correct IP (*************)
- ✅ Windows Firewall has rules for port 8080
- ✅ Database connection is working
- ✅ QR code generation is working
- ✅ Local access (localhost:8080) works

### ❌ Most Likely Issues

#### 1. **Network Routing Problem** (Most Common)
The router/switch may not be allowing communication between:
- Staff network: ***********-255 
- Server network: *************

**Solution:**
- Check router configuration
- Ensure inter-VLAN routing is enabled
- Verify no ACLs are blocking traffic between subnets

#### 2. **Windows Firewall Profile Issue**
The firewall rule might only be active for certain network profiles.

**Solution:**
```cmd
# Run as Administrator
netsh advfirewall firewall add rule name="NCHM Attendance" dir=in action=allow protocol=TCP localport=8080 profile=any
```

#### 3. **Apache Virtual Host Configuration**
The virtual host might be restricting access.

**Solution:** Check `C:\laragon\etc\apache2\sites-enabled\` for attendance.conf

## 🔧 Diagnostic Tools Created

### 1. Mobile Test Page
**URL for staff:** `http://*************:8080/attendance/mobile_test.php`

This page will show:
- ✅ Device type (mobile/desktop)
- ✅ Network access status
- ✅ IP authorization
- ✅ Database connection

### 2. Full Network Diagnostic
**URL:** `http://*************:8080/attendance/network_diagnostic.php`

Comprehensive diagnostic showing:
- Current connection details
- IP range validation
- System status
- Troubleshooting guide

### 3. Network Fix Script
**Run:** `network_fix.bat`

Automated script to check:
- IP configuration
- Apache listening status
- Firewall rules
- Service status

## 📱 Quick Test for Staff

1. **Connect to office Wi-Fi** (should get 172.31.18.x IP)
2. **Open browser** and go to: `http://*************:8080/attendance/mobile_test.php`
3. **Check status** - should show "ALL SYSTEMS GO" if working
4. **If working**, go to main system: `http://*************:8080/attendance/`

## 🔍 Step-by-Step Troubleshooting

### Step 1: Verify Network Connectivity
From a staff phone, try to ping the server:
```
ping *************
```
If this fails, it's a network routing issue.

### Step 2: Check Port Accessibility
From staff phone browser, try:
```
http://*************:8080/attendance/mobile_test.php
```

### Step 3: Check Firewall (Run as Administrator)
```cmd
netsh advfirewall firewall show rule name="Laragon 8080"
netsh advfirewall firewall show rule name="Laragon Apache 8080"
```

### Step 4: Verify Apache Configuration
Check if Apache is bound to all interfaces:
```cmd
netstat -an | findstr :8080
```
Should show: `0.0.0.0:8080` (not `127.0.0.1:8080`)

## 🚀 Immediate Solutions

### Solution 1: Restart Network Services
```cmd
# Restart Laragon (from Laragon control panel)
# Or restart Apache service
net stop Apache2.4
net start Apache2.4
```

### Solution 2: Add Comprehensive Firewall Rule
```cmd
# Run as Administrator
netsh advfirewall firewall add rule name="NCHM Attendance System" dir=in action=allow protocol=TCP localport=8080 profile=any remoteip=***********/24,*************
```

### Solution 3: Check Router Configuration
- Log into your network router/switch
- Verify inter-VLAN routing is enabled
- Check for any access control lists blocking 172.31.18.x → *************

## 📞 Contact Information

If issues persist:
1. Check the diagnostic pages first
2. Run the network_fix.bat script
3. Contact IT support with the diagnostic results

## 🔄 System Status URLs

- **Main System:** http://*************:8080/attendance/
- **Mobile Test:** http://*************:8080/attendance/mobile_test.php  
- **Full Diagnostic:** http://*************:8080/attendance/network_diagnostic.php
- **Admin Panel:** http://*************:8080/attendance/admin/simple_login.php

---
*Last Updated: July 15, 2025*
*NCHM Attendance System v2.0*
