<?php
// Test Excel Download Functionality
// This script tests if the Excel download is working properly

session_start();

// Simulate admin login for testing
$_SESSION['admin_logged_in'] = true;

echo "<h2>Testing Excel Download Functionality</h2>";

try {
    // Test database connection
    $pdo = new PDO("mysql:host=localhost;dbname=attendance_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ Database connection successful</p>";
    
    // Test if export_attendance.php exists
    if (file_exists('admin/export_attendance.php')) {
        echo "<p>✅ export_attendance.php file exists</p>";
    } else {
        echo "<p>❌ export_attendance.php file not found</p>";
    }
    
    // Test query
    $date = date('Y-m-d');
    $stmt = $pdo->prepare("
        SELECT s.name, s.eid_cid_permit, s.position_title, s.division,
               MIN(CASE WHEN al.action = 'clock_in' THEN TIME(al.timestamp) END) as clock_in_time,
               MAX(CASE WHEN al.action = 'clock_out' THEN TIME(al.timestamp) END) as clock_out_time,
               CASE
                   WHEN COUNT(CASE WHEN al.action = 'clock_in' THEN 1 END) > 0
                        AND COUNT(CASE WHEN al.action = 'clock_out' THEN 1 END) > 0
                   THEN 'Complete'
                   WHEN COUNT(CASE WHEN al.action = 'clock_in' THEN 1 END) > 0
                   THEN 'Present'
                   ELSE 'Absent'
               END as status
        FROM staff s
        LEFT JOIN attendance_logs al ON s.id = al.staff_id AND DATE(al.timestamp) = ?
        WHERE s.is_registered = 1
        GROUP BY s.id, s.name, s.eid_cid_permit, s.position_title, s.division
        ORDER BY s.name
        LIMIT 5
    ");
    $stmt->execute([$date]);
    $test_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>✅ Database query successful - Found " . count($test_data) . " records for today</p>";
    
    if (count($test_data) > 0) {
        echo "<h3>Sample Data:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Name</th><th>EID/CID</th><th>Position</th><th>Status</th></tr>";
        foreach ($test_data as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['eid_cid_permit']) . "</td>";
            echo "<td>" . htmlspecialchars($record['position_title'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($record['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>Test Links:</h3>";
    echo "<p><a href='admin/export_attendance.php?date=$date&format=excel' target='_blank'>🔗 Test Excel Download</a></p>";
    echo "<p><a href='admin/simple_dashboard.php' target='_blank'>🔗 Go to Admin Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f0f0f0; }
</style>
