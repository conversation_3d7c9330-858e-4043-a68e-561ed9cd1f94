<?php
echo "<h1>NCHM Attendance System - Network Configuration Test</h1>";

echo "<h2>Current Network Information</h2>";
echo "<strong>Server IP:</strong> " . ($_SERVER['SERVER_ADDR'] ?? 'Not available') . "<br>";
echo "<strong>Client IP:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'Not available') . "<br>";
echo "<strong>Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not available') . "<br>";
echo "<strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Not available') . "<br>";
echo "<strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not available') . "<br>";

echo "<h2>Network Interface Information</h2>";
if (function_exists('gethostbyname')) {
    echo "<strong>Hostname:</strong> " . gethostname() . "<br>";
    echo "<strong>Host IP:</strong> " . gethostbyname(gethostname()) . "<br>";
}

echo "<h2>Access URLs</h2>";
echo "<strong>Localhost:</strong> <a href='http://localhost:8080/attendance/'>http://localhost:8080/attendance/</a><br>";
echo "<strong>Network IP:</strong> <a href='http://*************:8080/attendance/'>http://*************:8080/attendance/</a><br>";

echo "<h2>Laragon Configuration Steps</h2>";
echo "<ol>";
echo "<li><strong>Apache Virtual Host:</strong> Configure Apache to listen on *************</li>";
echo "<li><strong>Windows Firewall:</strong> Allow Apache through firewall</li>";
echo "<li><strong>Network Binding:</strong> Bind Apache to all interfaces (0.0.0.0)</li>";
echo "</ol>";

echo "<h2>Test Links</h2>";
echo "<a href='index.php'>Main Attendance Page</a> | ";
echo "<a href='admin/simple_login.php'>Admin Login</a> | ";
echo "<a href='info.php'>System Info</a>";
?>
