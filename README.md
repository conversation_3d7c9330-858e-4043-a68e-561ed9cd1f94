# DHM QR Code Attendance System

A secure, local network-based staff attendance system for the Department of Hydro-Meteorology (DHM), Royal Government of Bhutan.

## 🌟 Features

### 🔐 Security First
- **Device Registration**: One-time QR code registration per staff member
- **Unique Device Binding**: Each staff member can only use their registered device
- **IP Range Validation**: Restricted to office Wi-Fi (172.31.18.x) and server IP
- **Anti-Proxy Protection**: Detects and blocks VPN/proxy usage
- **Device Fingerprinting**: Advanced device identification and validation
- **Rate Limiting**: Prevents brute force attacks

### 📱 QR Code System
- **Registration QR Codes**: Unique codes for each staff member's device registration
- **Daily Attendance QR Codes**: Generated daily for clock-in/clock-out
- **Automatic Expiry**: QR codes expire after set time periods
- **Usage Tracking**: Monitor QR code usage and prevent abuse

### 👥 Staff Management
- **Complete Staff Database**: Pre-loaded with 74 staff members
- **Role-Based Access**: Admin and Super Admin roles
- **Registration Status**: Track which staff have registered devices
- **Staff Information**: Name, EID/CID/Permit, Position, Division

### 📊 Attendance Tracking
- **Automatic Clock-In/Out**: System determines action based on last attendance
- **Real-Time Monitoring**: Live attendance dashboard
- **Attendance History**: Complete audit trail of all attendance actions
- **Division-Wise Tracking**: Monitor attendance by department divisions

### 📈 Reporting & Analytics
- **Daily Reports**: Automated email reports at 6:30 PM
- **Excel Export**: Export attendance data to Excel format
- **Print Support**: Print-friendly report layouts
- **Email Integration**: Automatic delivery to specified recipients
- **Attendance Statistics**: Present/absent counts and percentages

### 🎨 Meteorology-Themed UI
- **Weather-Inspired Colors**: Blues, grays, and earth tones
- **Professional Design**: Clean, modern interface
- **Responsive Layout**: Works on desktop, tablet, and mobile
- **Accessibility**: High contrast and readable fonts

## 🏗️ System Architecture

### Network Configuration
- **Server IP**: *************
- **Office Wi-Fi Range**: ***********-255
- **Local Network Only**: No internet access required
- **Secure Communication**: All traffic within organization network

### Technology Stack
- **Backend**: PHP 7.4+
- **Database**: MySQL 8.0+
- **QR Code Generation**: Endroid QR Code Library
- **Email**: PHPMailer
- **Frontend**: HTML5, CSS3, JavaScript
- **Server**: Apache/Nginx

### Database Structure
- **staff**: Staff member information and registration status
- **device_registrations**: Device registration and validation data
- **attendance_logs**: All attendance records with timestamps
- **qr_codes**: QR code generation and usage tracking
- **admin_users**: Administrative user accounts
- **system_logs**: Comprehensive audit trail
- **email_reports**: Email report delivery tracking

## 🚀 Quick Start

### 1. Installation
```bash
# Clone or extract the system files
cd /var/www/html/attendance

# Install PHP dependencies
composer install

# Set up database
mysql -u root -p < attendance_db.sql
mysql -u root -p attendance_db < database/enhanced_schema.sql

# Set permissions
chmod 755 qr_codes/ logs/
chown -R www-data:www-data qr_codes/ logs/
```

### 2. Configuration
```php
// Update config/database.php
private $host = '*************';
private $username = 'your_db_user';
private $password = 'your_db_password';

// Configure email settings
const SMTP_USERNAME = '<EMAIL>';
const SMTP_PASSWORD = 'your-app-password';
```

### 3. Access the System
- **Main Page**: http://*************:8080/
- **Admin Login**: http://*************:8080/admin/login.php
- **Default Admin**: username: `superadmin`, password: `admin123`

## 📋 Usage Workflow

### For Staff Members

#### Initial Registration:
1. **Get Registration QR**: Admin generates unique QR code for you
2. **Scan QR Code**: Use your phone to scan the registration QR
3. **Enter Device Info**: Provide your phone model details
4. **Save Credentials**: Note down your registration ID and device token

#### Daily Attendance:
1. **Connect to Wi-Fi**: Ensure you're on office Wi-Fi (172.31.18.x)
2. **Scan Daily QR**: Scan the daily attendance QR code
3. **Enter Credentials**: Input your registration ID and device token
4. **Automatic Action**: System determines clock-in or clock-out

### For Administrators

#### Staff Management:
- Add new staff members
- Generate registration QR codes
- Monitor registration status
- Update staff information

#### Attendance Monitoring:
- View real-time attendance dashboard
- Generate daily/weekly/monthly reports
- Export data to Excel
- Send email reports

#### System Administration:
- Manage admin users (Super Admin only)
- Monitor security logs
- Configure system settings
- Maintain QR codes

## 🔧 Advanced Features

### Security Monitoring
- **Real-Time Alerts**: Suspicious activity detection
- **IP Blocking**: Automatic blocking of unauthorized IPs
- **Device Validation**: Multi-factor device verification
- **Audit Trails**: Complete logging of all system activities

### Automated Operations
- **Daily Reports**: Automatic email at 6:30 PM
- **QR Code Rotation**: Daily generation of new attendance QR codes
- **Data Cleanup**: Automatic removal of expired QR codes
- **System Maintenance**: Scheduled database optimization

### Integration Capabilities
- **Email Integration**: SMTP support for notifications
- **Excel Export**: Native Excel file generation
- **Print Support**: Optimized print layouts
- **API Ready**: Extensible for future integrations

## 📊 Divisions Supported

The system supports all DHM divisions:
- **HWRSD**: Hydrology and Water Resources Services Division
- **MSD**: Meteorological Services Division
- **CSD**: Climate Services Division
- **TSRD**: Technical Services and Research Division
- **SECT**: Support Services, Engineering, Communication and Training
- **FWS**: Flood Warning Services

## 🛡️ Security Measures

### Access Control
- IP range restrictions (***********/24)
- Device fingerprint validation
- Session management and timeouts
- Role-based permissions

### Data Protection
- Encrypted device tokens
- Secure password hashing
- SQL injection prevention
- XSS protection

### Monitoring
- Failed login attempt tracking
- Suspicious activity alerts
- Rate limiting implementation
- Comprehensive audit logging

## 📞 Support

### System Requirements
- PHP 7.4+ with PDO MySQL extension
- MySQL 8.0+ database server
- Apache/Nginx web server
- Composer for dependency management

### Troubleshooting
- Check `logs/cron.log` for automated task issues
- Review `system_logs` table for security events
- Verify IP ranges and network connectivity
- Ensure QR code directories are writable

### Maintenance
- Regular database cleanup (monthly)
- Security log review (weekly)
- Admin password updates (quarterly)
- System backup (daily)

## 📄 License

This system is developed specifically for the Department of Hydro-Meteorology, Royal Government of Bhutan. All rights reserved.

---

**Department of Hydro-Meteorology**  
Royal Government of Bhutan  
System Version: 1.0  
Last Updated: July 2025
