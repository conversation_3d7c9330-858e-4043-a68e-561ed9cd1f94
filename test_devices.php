<?php
require_once 'classes/DatabaseManager.php';

$db = new DatabaseManager();

echo "<h2>Registered Devices</h2>";

try {
    $query = "SELECT dr.*, s.name, s.eid_cid_permit, s.position_title, s.division 
              FROM device_registrations dr 
              JOIN staff s ON dr.staff_id = s.id 
              WHERE dr.is_active = 1 
              ORDER BY dr.registration_date DESC";
    $stmt = $db->conn->prepare($query);
    $stmt->execute();
    $devices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($devices)) {
        echo "<p>No registered devices found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Name</th><th>EID/CID</th><th>Registration ID</th><th>Device <PERSON></th><th>Device Model</th><th>Registration Date</th></tr>";
        
        foreach ($devices as $device) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($device['name']) . "</td>";
            echo "<td>" . htmlspecialchars($device['eid_cid_permit']) . "</td>";
            echo "<td>" . htmlspecialchars($device['registration_id']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($device['device_token'], 0, 20)) . "...</td>";
            echo "<td>" . htmlspecialchars($device['device_model']) . "</td>";
            echo "<td>" . htmlspecialchars($device['registration_date']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<br><h2>Staff List</h2>";

try {
    $staff_list = $db->getAllStaff();
    
    if (empty($staff_list)) {
        echo "<p>No staff found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>EID/CID</th><th>Position</th><th>Division</th><th>Is Registered</th></tr>";
        
        foreach ($staff_list as $staff) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($staff['id']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['name']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['eid_cid_permit']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['position_title'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($staff['division'] ?? 'N/A') . "</td>";
            echo "<td>" . ($staff['is_registered'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
