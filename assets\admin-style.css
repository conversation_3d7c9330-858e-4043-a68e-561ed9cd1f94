/* Admin Dashboard Styles - Meteorology & Hydrology Theme */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Meteorology & Hydrology Color Palette */
    --primary-blue: #2c5aa0;
    --secondary-blue: #4a90e2;
    --water-blue: #1e88e5;
    --sky-blue: #87ceeb;
    --cloud-gray: #b0bec5;
    --storm-gray: #546e7a;
    --rain-blue: #0277bd;
    --success-green: #4caf50;
    --warning-orange: #ff9800;
    --error-red: #f44336;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --background-light: #f8f9fa;
    --white: #ffffff;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background-light);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--water-blue) 100%);
    color: var(--white);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Navigation Styles */
.nav-menu {
    background: var(--white);
    border-bottom: 1px solid #e1e8ed;
    padding: 0 2rem;
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    gap: 2rem;
}

.nav-item {
    padding: 1rem 0;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.nav-item:hover, .nav-item.active {
    color: var(--water-blue);
    border-bottom-color: var(--water-blue);
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: var(--text-dark);
    font-size: 2rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background-light);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
}

.card-content {
    padding: 2rem;
}

/* Buttons */
.btn {
    background: linear-gradient(45deg, var(--secondary-blue), var(--water-blue));
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: transform 0.2s, box-shadow 0.2s;
    border: 2px solid transparent;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(30, 136, 229, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-green), #45a049);
}

.btn-success:hover {
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.btn-danger {
    background: linear-gradient(45deg, var(--error-red), #d32f2f);
}

.btn-danger:hover {
    box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-orange), #f57c00);
}

.btn-warning:hover {
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

.table th, .table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
}

.table th {
    background: var(--background-light);
    font-weight: 600;
    color: var(--text-dark);
    border-bottom: 2px solid var(--cloud-gray);
}

.table tbody tr:hover {
    background: rgba(30, 136, 229, 0.05);
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--water-blue);
    box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

/* Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-green);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge-danger {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-red);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.badge-warning {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-orange);
    border: 1px solid rgba(255, 152, 0, 0.3);
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-green);
    border-left-color: var(--success-green);
}

.alert-error {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-red);
    border-left-color: var(--error-red);
}

.alert-warning {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-orange);
    border-left-color: var(--warning-orange);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    border-top: 4px solid var(--water-blue);
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    color: var(--water-blue);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* QR Code Display */
.qr-display {
    text-align: center;
    padding: 2rem;
}

.qr-code-img {
    max-width: 300px;
    width: 100%;
    border: 3px solid var(--water-blue);
    border-radius: 10px;
    margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-content {
        flex-direction: column;
        gap: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        padding: 0 1rem;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: var(--white) !important;
        color: black !important;
    }
    
    .card, .table {
        box-shadow: none !important;
        border: 1px solid #ccc;
    }
    
    .header {
        background: var(--white) !important;
        color: black !important;
        border-bottom: 2px solid #ccc;
    }
}
