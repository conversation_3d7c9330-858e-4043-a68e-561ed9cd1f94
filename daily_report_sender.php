<?php
/**
 * Daily Attendance Report Email Sender
 * Automatically sends daily attendance reports to specified email addresses
 * Run this script daily at 8:00 PM via Windows Task Scheduler
 */

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Asia/Thimphu');

// Load configuration
$config = require_once 'email_config.php';

// Log file for debugging
$log_file = 'logs/daily_email_' . date('Y-m') . '.log';

function writeLog($message) {
    global $log_file;
    if (!file_exists('logs')) {
        mkdir('logs', 0777, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND);
}

writeLog("Daily report sender started");

try {
    // Connect to database
    $pdo = new PDO("mysql:host=localhost;dbname=attendance_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get today's date
    $today = date('Y-m-d');
    $today_formatted = date('F j, Y');
    
    writeLog("Generating report for date: $today");
    
    // Get attendance data for today (same query as dashboard)
    $stmt = $pdo->prepare("
        SELECT s.name, s.eid_cid_permit, s.position_title, s.division,
               MIN(CASE WHEN al.action = 'clock_in' THEN TIME(al.timestamp) END) as clock_in_time,
               MAX(CASE WHEN al.action = 'clock_out' THEN TIME(al.timestamp) END) as clock_out_time,
               CASE 
                   WHEN COUNT(CASE WHEN al.action = 'clock_in' THEN 1 END) > 0 
                        AND COUNT(CASE WHEN al.action = 'clock_out' THEN 1 END) > 0 
                   THEN 'Complete'
                   WHEN COUNT(CASE WHEN al.action = 'clock_in' THEN 1 END) > 0 
                   THEN 'Present'
                   ELSE 'Absent'
               END as status,
               DATE(al.timestamp) as attendance_date
        FROM staff s
        LEFT JOIN attendance_logs al ON s.id = al.staff_id AND DATE(al.timestamp) = ?
        WHERE s.is_registered = 1
        GROUP BY s.id, s.name, s.eid_cid_permit, s.position_title, s.division
        ORDER BY s.name
    ");
    $stmt->execute([$today]);
    $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    writeLog("Found " . count($attendance_data) . " attendance records");
    
    // Generate statistics
    $total_staff = count($attendance_data);
    $present_count = 0;
    $complete_count = 0;
    $absent_count = 0;
    
    foreach ($attendance_data as $record) {
        switch ($record['status']) {
            case 'Complete':
                $complete_count++;
                $present_count++;
                break;
            case 'Present':
                $present_count++;
                break;
            case 'Absent':
                $absent_count++;
                break;
        }
    }
    
    // Generate Excel content
    $excel_content = generateExcelContent($attendance_data, $today_formatted, $total_staff, $present_count, $complete_count, $absent_count);
    
    // Save Excel file temporarily
    $temp_file = 'temp/daily_report_' . $today . '.xls';
    if (!file_exists('temp')) {
        mkdir('temp', 0777, true);
    }
    file_put_contents($temp_file, $excel_content);
    
    writeLog("Excel file generated: $temp_file");
    
    // Send email
    $email_sent = sendEmailWithAttachment($config, $attendance_data, $temp_file, $today_formatted, $total_staff, $present_count, $complete_count, $absent_count);
    
    if ($email_sent) {
        writeLog("Email sent successfully to: " . implode(', ', $config['recipients']));
    } else {
        writeLog("Failed to send email");
    }
    
    // Clean up temporary file
    if (file_exists($temp_file)) {
        unlink($temp_file);
        writeLog("Temporary file cleaned up");
    }
    
} catch (Exception $e) {
    writeLog("Error: " . $e->getMessage());
    echo "Error: " . $e->getMessage() . "\n";
}

writeLog("Daily report sender completed\n");

function generateExcelContent($data, $date, $total, $present, $complete, $absent) {
    $excel = '<?xml version="1.0"?><?mso-application progid="Excel.Sheet"?>
    <Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">
    <Worksheet ss:Name="Daily Attendance">
    <Table>';
    
    // Header
    $excel .= '<Row>
        <Cell><Data ss:Type="String">NCHM Daily Attendance Report</Data></Cell>
    </Row>
    <Row>
        <Cell><Data ss:Type="String">Date: ' . $date . '</Data></Cell>
    </Row>
    <Row></Row>';
    
    // Statistics
    $excel .= '<Row>
        <Cell><Data ss:Type="String">Summary Statistics</Data></Cell>
    </Row>
    <Row>
        <Cell><Data ss:Type="String">Total Registered Staff: ' . $total . '</Data></Cell>
    </Row>
    <Row>
        <Cell><Data ss:Type="String">Present Today: ' . $present . '</Data></Cell>
    </Row>
    <Row>
        <Cell><Data ss:Type="String">Complete Attendance: ' . $complete . '</Data></Cell>
    </Row>
    <Row>
        <Cell><Data ss:Type="String">Absent: ' . $absent . '</Data></Cell>
    </Row>
    <Row></Row>';
    
    // Table headers
    $excel .= '<Row>
        <Cell><Data ss:Type="String">Name</Data></Cell>
        <Cell><Data ss:Type="String">EID/CID/Permit</Data></Cell>
        <Cell><Data ss:Type="String">Position</Data></Cell>
        <Cell><Data ss:Type="String">Division</Data></Cell>
        <Cell><Data ss:Type="String">Clock In Time</Data></Cell>
        <Cell><Data ss:Type="String">Clock Out Time</Data></Cell>
        <Cell><Data ss:Type="String">Status</Data></Cell>
    </Row>';
    
    // Data rows
    foreach ($data as $record) {
        $excel .= '<Row>
            <Cell><Data ss:Type="String">' . htmlspecialchars($record['name']) . '</Data></Cell>
            <Cell><Data ss:Type="String">' . htmlspecialchars($record['eid_cid_permit']) . '</Data></Cell>
            <Cell><Data ss:Type="String">' . htmlspecialchars($record['position_title'] ?? 'N/A') . '</Data></Cell>
            <Cell><Data ss:Type="String">' . htmlspecialchars($record['division'] ?? 'N/A') . '</Data></Cell>
            <Cell><Data ss:Type="String">' . ($record['clock_in_time'] ?? 'N/A') . '</Data></Cell>
            <Cell><Data ss:Type="String">' . ($record['clock_out_time'] ?? 'N/A') . '</Data></Cell>
            <Cell><Data ss:Type="String">' . $record['status'] . '</Data></Cell>
        </Row>';
    }
    
    $excel .= '</Table></Worksheet></Workbook>';
    
    return $excel;
}

function sendEmailWithAttachment($config, $data, $attachment_path, $date, $total, $present, $complete, $absent) {
    // For now, let's create a simple email without attachment to test
    // This will work with basic PHP mail() function

    $subject = str_replace('{date}', $date, $config['email']['subject']);
    $email_body = generateEmailBody($date, $total, $present, $complete, $absent, count($data));

    // Simple headers for HTML email
    $headers = "From: NCHM Attendance System <<EMAIL>>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";

    // Send to each recipient
    $success = true;
    foreach ($config['recipients'] as $recipient) {
        writeLog("Attempting to send email to: $recipient");

        // Add attachment info to email body since basic mail() has issues with attachments
        $full_body = $email_body . "<br><br><p><strong>Note:</strong> Excel file is available for download from the admin dashboard.</p>";

        if (mail($recipient, $subject, $full_body, $headers)) {
            writeLog("Email sent successfully to: $recipient");
        } else {
            $success = false;
            writeLog("Failed to send email to: $recipient");
        }
    }

    return $success;
}

function generateEmailBody($date, $total, $present, $complete, $absent, $records_count) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px; }
            .stats { display: flex; justify-content: space-around; margin: 20px 0; }
            .stat-box { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; min-width: 120px; }
            .stat-number { font-size: 24px; font-weight: bold; color: #2c3e50; }
            .stat-label { color: #7f8c8d; margin-top: 5px; }
            .footer { margin-top: 30px; padding: 15px; background: #ecf0f1; border-radius: 5px; font-size: 12px; color: #7f8c8d; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h1>NCHM Daily Attendance Report</h1>
            <h2>National Center for Hydrology and Meteorology</h2>
            <p>Date: $date</p>
        </div>

        <div class='stats'>
            <div class='stat-box'>
                <div class='stat-number'>$total</div>
                <div class='stat-label'>Total Staff</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>$present</div>
                <div class='stat-label'>Present Today</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>$complete</div>
                <div class='stat-label'>Complete Attendance</div>
            </div>
            <div class='stat-box'>
                <div class='stat-number'>$absent</div>
                <div class='stat-label'>Absent</div>
            </div>
        </div>

        <p><strong>Attendance Summary:</strong></p>
        <ul>
            <li>Total Registered Staff: $total</li>
            <li>Staff Present Today: $present</li>
            <li>Complete Attendance (Clock In & Out): $complete</li>
            <li>Absent Staff: $absent</li>
            <li>Attendance Rate: " . ($total > 0 ? round(($present / $total) * 100, 1) : 0) . "%</li>
        </ul>

        <p>Please find the detailed attendance report attached as an Excel file.</p>

        <div class='footer'>
            <p>This is an automated email from the NCHM Attendance System.</p>
            <p>Generated on: " . date('Y-m-d H:i:s') . " (Bhutan Time)</p>
            <p>© " . date('Y') . " National Center for Hydrology and Meteorology, Royal Government of Bhutan</p>
        </div>
    </body>
    </html>";
}
?>
