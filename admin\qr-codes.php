<?php
session_start();
require_once '../config/database.php';
require_once '../classes/DatabaseManager.php';
require_once '../classes/QRCodeManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Check session timeout
if (time() - $_SESSION['login_time'] > SystemConfig::SESSION_TIMEOUT) {
    session_destroy();
    header('Location: login.php?timeout=1');
    exit;
}

$db = new DatabaseManager();
$qr_manager = new QRCodeManager();

$message = '';
$error = '';
$qr_codes = [];

// Get server IP for QR code links
$server_ip = $_SERVER['SERVER_ADDR'] ?? '*************';
$base_url = "http://{$server_ip}:8000";

// Handle QR code generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'generate_daily') {
        // Generate daily attendance QR code
        try {
            $daily_qr = $qr_manager->getCurrentDailyQR();
            $message = 'Daily attendance QR code generated successfully!';
            $qr_codes[] = [
                'type' => 'daily',
                'title' => 'Daily Attendance QR Code',
                'subtitle' => 'Valid for ' . date('F j, Y'),
                'url' => $daily_qr['url'],
                'image' => $daily_qr['image_path'],
                'code' => $daily_qr['code']
            ];
        } catch (Exception $e) {
            $error = 'Failed to generate daily QR code: ' . $e->getMessage();
        }
    } elseif ($action === 'generate_attendance_all') {
        // Generate attendance QR codes for all registered staff
        try {
            $registered_staff = $db->getRegisteredStaff();
            
            if (empty($registered_staff)) {
                $error = 'No registered staff found.';
            } else {
                foreach ($registered_staff as $staff) {
                    // Generate daily token for this staff member
                    $token = hash('sha256', $staff['id'] . $staff['registration_id'] . date('Y-m-d'));
                    
                    // Create attendance URL
                    $attendance_url = $base_url . '/staff_attendance.php?staff_id=' . $staff['id'] . '&token=' . $token;
                    
                    // Generate QR code
                    $qr_code = [
                        'type' => 'staff_attendance',
                        'title' => $staff['name'],
                        'subtitle' => $staff['eid_cid_permit'] . ' - ' . ($staff['position_title'] ?? 'Staff'),
                        'url' => $attendance_url,
                        'image' => generateQRCodeImage($attendance_url, $staff['id']),
                        'staff_id' => $staff['id']
                    ];
                    
                    $qr_codes[] = $qr_code;
                }
                
                $message = 'Generated ' . count($qr_codes) . ' staff attendance QR codes successfully!';
            }
        } catch (Exception $e) {
            $error = 'Failed to generate staff attendance QR codes: ' . $e->getMessage();
        }
    } elseif ($action === 'generate_attendance_single') {
        $staff_id = $_POST['staff_id'] ?? '';
        
        if (empty($staff_id)) {
            $error = 'Please select a staff member.';
        } else {
            try {
                $staff = $db->getStaffById($staff_id);
                
                if (!$staff || !$staff['is_registered']) {
                    $error = 'Staff member not found or not registered.';
                } else {
                    // Generate daily token for this staff member
                    $token = hash('sha256', $staff['id'] . $staff['registration_id'] . date('Y-m-d'));
                    
                    // Create attendance URL
                    $attendance_url = $base_url . '/staff_attendance.php?staff_id=' . $staff['id'] . '&token=' . $token;
                    
                    // Generate QR code
                    $qr_code = [
                        'type' => 'staff_attendance',
                        'title' => $staff['name'],
                        'subtitle' => $staff['eid_cid_permit'] . ' - ' . ($staff['position_title'] ?? 'Staff'),
                        'url' => $attendance_url,
                        'image' => generateQRCodeImage($attendance_url, $staff['id']),
                        'staff_id' => $staff['id']
                    ];
                    
                    $qr_codes[] = $qr_code;
                    $message = 'Generated attendance QR code for ' . $staff['name'] . ' successfully!';
                }
            } catch (Exception $e) {
                $error = 'Failed to generate QR code: ' . $e->getMessage();
            }
        }
    } elseif ($action === 'generate_registration') {
        $staff_id = $_POST['staff_id'] ?? '';
        
        if (empty($staff_id)) {
            $error = 'Please select a staff member.';
        } else {
            try {
                $registration_qr = $qr_manager->generateRegistrationQR($staff_id);
                $staff = $db->getStaffById($staff_id);
                
                $qr_codes[] = [
                    'type' => 'registration',
                    'title' => 'Registration QR - ' . $staff['name'],
                    'subtitle' => $staff['eid_cid_permit'] . ' - Device Registration',
                    'url' => $registration_qr['url'],
                    'image' => $registration_qr['image_path'],
                    'code' => $registration_qr['code']
                ];
                
                $message = 'Generated registration QR code for ' . $staff['name'] . ' successfully!';
            } catch (Exception $e) {
                $error = 'Failed to generate registration QR code: ' . $e->getMessage();
            }
        }
    }
}

// Function to generate QR code image
function generateQRCodeImage($data, $staff_id) {
    $filename = 'qr_codes/attendance_staff_' . $staff_id . '_' . date('Ymd') . '.png';
    
    if (!is_dir('../qr_codes')) {
        mkdir('../qr_codes', 0755, true);
    }
    
    // Use QR Server API to generate QR code
    $qr_url = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($data);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'method' => 'GET',
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $qr_image_data = file_get_contents($qr_url, false, $context);
    
    if ($qr_image_data !== false) {
        file_put_contents('../' . $filename, $qr_image_data);
        return $filename;
    } else {
        // Fallback: create a simple text file with the URL
        file_put_contents('../' . $filename . '.txt', $data);
        return $filename . '.txt';
    }
}

// Get all staff for dropdowns
$all_staff = $db->getAllStaff();
$registered_staff = $db->getRegisteredStaff();
$unregistered_staff = array_filter($all_staff, function($staff) {
    return !$staff['is_registered'];
});

$page_title = 'QR Code Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - NCHM Admin</title>
    <link rel="stylesheet" href="../assets/admin-style.css">
    <style>
        .qr-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .qr-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }

        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .qr-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .qr-card h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .qr-card p {
            color: #6c757d;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .qr-image {
            max-width: 200px;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .qr-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            word-break: break-all;
            color: #495057;
            margin-bottom: 15px;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="../assets/nchm-logo.png" alt="NCHM Logo" class="logo">
                <div class="title-section">
                    <h1>NCHM Admin Panel</h1>
                    <p>QR Code Management System</p>
                </div>
            </div>
            <div class="user-info">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_name']); ?></span>
                <span class="badge badge-success"><?php echo ucfirst($_SESSION['admin_role']); ?></span>
                <a href="logout.php" class="btn btn-danger">Logout</a>
            </div>
        </div>
    </div>

    <nav class="nav-menu">
        <div class="nav-content">
            <a href="dashboard.php" class="nav-item">Dashboard</a>
            <a href="staff.php" class="nav-item">Staff Management</a>
            <a href="attendance.php" class="nav-item">Attendance Records</a>
            <a href="reports.php" class="nav-item">Reports</a>
            <a href="qr-codes.php" class="nav-item active">QR Codes</a>
            <?php if ($_SESSION['admin_role'] === 'super_admin'): ?>
                <a href="admin-users.php" class="nav-item">Admin Users</a>
            <?php endif; ?>
        </div>
    </nav>

    <div class="main-content">
        <h1>QR Code Management</h1>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <strong>✅ Success:</strong> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <strong>⚠️ Error:</strong> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Daily Attendance QR Code -->
        <div class="qr-section">
            <h3>📅 Daily Attendance QR Code</h3>
            <p>Generate a single QR code for all staff to scan for attendance (traditional method)</p>
            <form method="POST">
                <button type="submit" name="action" value="generate_daily" class="btn btn-success">
                    🎯 Generate Daily QR Code
                </button>
            </form>
        </div>

        <!-- Staff Attendance QR Codes -->
        <div class="qr-section">
            <h3>👥 Individual Staff Attendance QR Codes</h3>
            <p>Generate personalized QR codes that link directly to staff attendance pages</p>
            
            <form method="POST">
                <button type="submit" name="action" value="generate_attendance_all" class="btn btn-success">
                    🎯 Generate QR Codes for All Registered Staff
                </button>
            </form>

            <hr style="margin: 20px 0;">

            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="staff_id_attendance">Generate for Specific Staff:</label>
                        <select name="staff_id" id="staff_id_attendance" required>
                            <option value="">Select Staff Member</option>
                            <?php foreach ($registered_staff as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>">
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['eid_cid_permit'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" name="action" value="generate_attendance_single" class="btn btn-primary">
                        📱 Generate QR Code
                    </button>
                </div>
            </form>
        </div>

        <!-- Registration QR Codes -->
        <div class="qr-section">
            <h3>🔐 Device Registration QR Codes</h3>
            <p>Generate QR codes for staff to register their devices</p>
            
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="staff_id_registration">Select Staff Member:</label>
                        <select name="staff_id" id="staff_id_registration" required>
                            <option value="">Select Staff Member</option>
                            <?php foreach ($unregistered_staff as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>">
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['eid_cid_permit'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" name="action" value="generate_registration" class="btn btn-primary">
                        🔒 Generate Registration QR
                    </button>
                </div>
            </form>
        </div>

        <!-- Generated QR Codes Display -->
        <?php if (!empty($qr_codes)): ?>
            <div class="qr-grid">
                <?php foreach ($qr_codes as $qr): ?>
                    <div class="qr-card">
                        <h4><?php echo htmlspecialchars($qr['title']); ?></h4>
                        <p><?php echo htmlspecialchars($qr['subtitle']); ?></p>
                        
                        <?php if (file_exists('../' . $qr['image']) && pathinfo($qr['image'], PATHINFO_EXTENSION) === 'png'): ?>
                            <img src="../<?php echo $qr['image']; ?>" alt="QR Code" class="qr-image">
                        <?php else: ?>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
                                <p>QR Code Image</p>
                                <small>Scan with camera app</small>
                            </div>
                        <?php endif; ?>
                        
                        <div class="qr-url">
                            <strong>URL:</strong><br>
                            <?php echo htmlspecialchars($qr['url']); ?>
                        </div>
                        
                        <button onclick="copyToClipboard('<?php echo htmlspecialchars($qr['url']); ?>')" class="btn btn-primary btn-small">
                            📋 Copy URL
                        </button>
                        
                        <?php if (isset($qr['code'])): ?>
                            <p style="margin-top: 10px; font-size: 12px; color: #6c757d;">
                                <strong>Code:</strong> <?php echo htmlspecialchars($qr['code']); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('URL copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('URL copied to clipboard!');
                } catch (err) {
                    alert('Failed to copy URL');
                }
                document.body.removeChild(textArea);
            });
        }
    </script>
</body>
</html>
