<!DOCTYPE html>
<html>
<head>
    <title>Laragon Diagnostic Guide</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { background: #f0f8ff; padding: 15px; margin: 10px 0; border-left: 4px solid #007acc; }
        .error { background: #ffe6e6; border-left-color: #cc0000; }
        .success { background: #e6ffe6; border-left-color: #00cc00; }
        .code { background: #f5f5f5; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 Laragon Connection Diagnostic</h1>
    
    <div class="error">
        <h2>❌ Problem: ERR_CONNECTION_REFUSED</h2>
        <p>This means Apache is not running or not listening on port 80.</p>
    </div>

    <h2>📋 Step-by-Step Fix</h2>

    <div class="step">
        <h3>Step 1: Check Laragon Status</h3>
        <ol>
            <li>Look at your system tray (bottom-right corner)</li>
            <li>Find the Laragon icon (blue/white icon)</li>
            <li>Right-click on it</li>
            <li>Check if you see:
                <ul>
                    <li>✅ Apache: Started</li>
                    <li>✅ MySQL: Started</li>
                </ul>
            </li>
            <li>If not, click <strong>"Start All"</strong></li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 2: Test Laragon Default Page</h3>
        <ol>
            <li>Open browser</li>
            <li>Go to: <a href="http://localhost">http://localhost</a></li>
            <li>You should see Laragon welcome page</li>
            <li>If not, Apache is not running properly</li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 3: Check Port 80</h3>
        <ol>
            <li>Press <kbd>Win + R</kbd></li>
            <li>Type: <code>cmd</code> and press Enter</li>
            <li>Type: <code>netstat -an | findstr :80</code></li>
            <li>You should see: <code>0.0.0.0:80 LISTENING</code></li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 4: Restart Laragon Services</h3>
        <ol>
            <li>Right-click Laragon tray icon</li>
            <li>Click <strong>"Stop All"</strong></li>
            <li>Wait 5 seconds</li>
            <li>Click <strong>"Start All"</strong></li>
            <li>Wait for green checkmarks</li>
        </ol>
    </div>

    <div class="step">
        <h3>Step 5: Check for Port Conflicts</h3>
        <p>Other programs might be using port 80:</p>
        <ul>
            <li><strong>Skype:</strong> Tools → Options → Advanced → Connection → Uncheck "Use port 80"</li>
            <li><strong>IIS:</strong> Windows Features → Uncheck "Internet Information Services"</li>
            <li><strong>Other web servers:</strong> Stop XAMPP, WAMP, etc.</li>
        </ul>
    </div>

    <div class="step">
        <h3>Step 6: Alternative Port</h3>
        <p>If port 80 is blocked, use port 8080:</p>
        <ol>
            <li>Right-click Laragon → Apache → httpd.conf</li>
            <li>Find: <code>Listen 80</code></li>
            <li>Change to: <code>Listen 8080</code></li>
            <li>Save and restart Apache</li>
            <li>Access via: <a href="http://localhost:8080/attendance/">http://localhost:8080/attendance/</a></li>
        </ol>
    </div>

    <h2>🧪 Quick Tests</h2>
    
    <div class="step">
        <h3>Test 1: Basic Connectivity</h3>
        <p>Try these URLs in order:</p>
        <ol>
            <li><a href="http://localhost:8080">http://localhost:8080</a> (Laragon home)</li>
            <li><a href="http://localhost:8080/attendance/">http://localhost:8080/attendance/</a> (Your app folder)</li>
            <li><a href="http://localhost:8080/attendance/debug.php">http://localhost:8080/attendance/debug.php</a> (Simple PHP test)</li>
        </ol>
    </div>

    <div class="step">
        <h3>Test 2: File Access</h3>
        <p>Check if files exist:</p>
        <div class="code">
            C:\laragon\www\attendance\index.php<br>
            C:\laragon\www\attendance\network_setup.php<br>
            C:\laragon\www\attendance\debug.php
        </div>
    </div>

    <h2>🆘 If Nothing Works</h2>
    
    <div class="error">
        <h3>Nuclear Option: Reinstall Laragon</h3>
        <ol>
            <li>Download latest Laragon from <a href="https://laragon.org/download/">laragon.org</a></li>
            <li>Backup your <code>C:\laragon\www\attendance</code> folder</li>
            <li>Uninstall current Laragon</li>
            <li>Install fresh Laragon</li>
            <li>Copy back your attendance folder</li>
        </ol>
    </div>

    <div class="success">
        <h3>✅ Success Indicators</h3>
        <p>You'll know it's working when:</p>
        <ul>
            <li>Laragon tray icon shows green checkmarks</li>
            <li><a href="http://localhost">http://localhost</a> shows Laragon page</li>
            <li><a href="http://localhost/attendance/">http://localhost/attendance/</a> shows your attendance system</li>
        </ul>
    </div>

    <hr>
    <p><strong>Need help?</strong> Tell me what you see when you:</p>
    <ol>
        <li>Right-click Laragon tray icon</li>
        <li>Try to access <a href="http://localhost">http://localhost</a></li>
        <li>Run the port check command</li>
    </ol>
</body>
</html>
