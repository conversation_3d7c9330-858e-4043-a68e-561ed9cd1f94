-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Jul 15, 2025 at 03:33 PM
-- Server version: 8.4.3
-- PHP Version: 8.3.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `attendance_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `device_registrations`
--

CREATE TABLE `device_registrations` (
  `id` int NOT NULL,
  `staff_id` int NOT NULL,
  `registration_id` varchar(255) NOT NULL,
  `device_token` varchar(255) NOT NULL,
  `device_model` varchar(500) DEFAULT NULL,
  `device_fingerprint` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `registration_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `last_used` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `device_registrations`
--

INSERT INTO `device_registrations` (`id`, `staff_id`, `registration_id`, `device_token`, `device_model`, `device_fingerprint`, `ip_address`, `user_agent`, `registration_date`, `is_active`, `last_used`) VALUES
(1, 230, 'REG_230_20250709_163944', '21d893147c0f0a5f28f4f1b4aa0c269fc80a4e4b9f2bdf37c822f02428575e14', 'iPhone', '37c6809abf1ade0645c45614f8370c0e', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 10:39:44', 1, NULL),
(2, 230, 'REG_230_20250709_164254', '0124bf5312e4d31232ab9aa1bfd93f0faecbc018b6955bdf10e2c046fdb05da4', 'iPhone', '37c6809abf1ade0645c45614f8370c0e', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 10:42:54', 1, NULL),
(3, 238, 'REG_238_20250709_165503', '96898ae16d7367269690dd5baede42a56a60a1fdee3bfc20e603bd8039c32f7a', 'iPhone', '43a2d983173a777606eb76894379916a', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 10:55:03', 1, NULL),
(4, 281, 'REG_281_20250709_165735', 'fe1170296415a5cd39bb01c0c59944c2a4d29ee59ae4b4e5ce6a76e663645fb9', 'iPhone', '96fb5de43a6f753905c65bd4a9ba83f0', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 10:57:35', 1, NULL),
(5, 230, 'REG_230_20250709_190011', 'c7766e76c0a087581c27a7d99440410295d23f3947ed90bd5efec68058bd6e3c', 'iPhone', '37c6809abf1ade0645c45614f8370c0e', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 13:00:11', 1, NULL),
(6, 230, 'REG_230_20250709_190218', '00eeeec484205132f37c35f0cd48f57d3dab1fdbab29139ee756ddcbde1ea629', 'iPhone', '37c6809abf1ade0645c45614f8370c0e', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 13:02:18', 1, NULL),
(7, 245, 'REG_245_20250709_201543', '617d76a5624599d2e5b043ea310b3b0375c28c50a68341b30ed8399eacdb71a4', 'Android 11', '98aaacc4c2c7a4d8ee7dfac86bdc0c5b', '*************', 'Mozilla/5.0 (Linux; Android 11; vivo 1904) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 VivoBrowser/********', '2025-07-09 14:15:43', 1, NULL),
(8, 245, 'REG_245_20250709_211029', '2f7690ac63d54088baa801264f117af0adfba19c7d2fbb54d4391786e423345c', 'Android 11', '98aaacc4c2c7a4d8ee7dfac86bdc0c5b', '*************', 'Mozilla/5.0 (Linux; Android 11; vivo 1904) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/87.0.4280.141 Mobile Safari/537.36 VivoBrowser/********', '2025-07-09 15:10:29', 1, '2025-07-09 15:39:34'),
(9, 230, 'REG_230_20250709_214035', '284105f9fc3e424fcf461278ecb4dab24bf6c553a166902f95fdc96e9e4d63d6', 'iPhone', '425d0e6d023081f3ed7f128a4fb0d6e5', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 15:40:35', 1, NULL),
(10, 230, 'REG_230_20250709_214317', '3d13d1adde1c8bd047321527c44966ea7e4e300938374650e78be85ff59ba022', 'iPhone', '425d0e6d023081f3ed7f128a4fb0d6e5', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-09 15:43:17', 1, NULL),
(11, 266, 'REG_266_20250709_220215', '00352443690a0244848fd6bec0398ceb0d75a4f082cba3e24dafbf4f3d9b07bd', 'Android 10', 'f6a0f015da79dd46d4ac620a6526a5b2', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-09 16:02:15', 1, '2025-07-09 16:16:09'),
(12, 230, 'REG_230_20250709_221154', '1fcf4bc1f1faa0e48bf3eb6e5c37ffe3d7d935afe5107503a0a3068ff8de2f42', 'iPhone', 'f2add673f09c39649efac1903df3c294', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-09 16:11:54', 1, '2025-07-10 08:53:31'),
(13, 281, 'REG_281_20250709_221718', '0f0ba819a5bdfce6ea72f8f9d156dcb7d562a4e49e8b4a25e7ce48857fe71fa4', 'iPhone', '22de6fc0e6c40ae71441f5bac33e5814', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-09 16:17:18', 1, '2025-07-09 16:18:48'),
(14, 258, 'REG_258_20250709_222050', '4b90993c7f6fffb7d393b585ac0e1ee72d76f623223ad34235057fadbebe2e20', 'Android 13', '0a34fd848d5cbb1e03b92fb8b500d656', '************', 'Mozilla/5.0 (Android 13; Mobile; rv:140.0) Gecko/140.0 Firefox/140.0', '2025-07-09 16:20:50', 1, '2025-07-09 16:21:37'),
(15, 238, 'REG_238_20250709_225526', '6371583687d769c42530522fd8f88a6c74514a64e7bb2ab066549fc585c5eb30', 'iPhone', 'bb1f550ab72161d2d3c0687300969ba2', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-09 16:55:26', 1, '2025-07-09 16:56:18'),
(16, 283, 'REG_283_20250710_151719', 'a4b3a5702526705cb70301306456f9712e2a3c3bd0a4e7577408f32110b178da', 'Android 10', '78816b508b48c787938d4848e22832fd', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 09:17:19', 1, NULL),
(17, 276, 'REG_276_20250710_151916', '648c85cbd954153403e5a998e5b0e0fde02a32616b77945a1f2847dbf8ade839', 'Android 10', '59926eb387b4a9fa3a868ea04032c12d', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 09:19:16', 1, '2025-07-10 09:20:03'),
(18, 280, 'REG_280_20250710_160742', '35d72e822c24582cad1d1d768430c9e0fdb664b61c89af926b586f171f39acff', 'iPhone', 'd317cc851727bb94f9882db52eb60f53', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/137.0.7151.79 Mobile/15E148 Safari/604.1', '2025-07-10 10:07:42', 1, NULL),
(19, 255, 'REG_255_20250710_160834', 'c4b75c086d7e6f7f687857ddd75cf012cf95c063b320bb2611c4b88091a765b8', 'iPhone', '90bac127b51783df29b7df92b7c4d7cc', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-10 10:08:34', 1, NULL),
(20, 287, 'REG_287_20250710_161020', '0789f916fdf947b70ad738d7478bf027279a73cc725843bf927aa43e9751ea87', 'iPhone', '4f6498c03bb8d5d61764abaf6173ea61', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-10 10:10:20', 1, NULL),
(21, 234, 'REG_234_20250710_161115', '883e9de1245beb34f891f2ed653894b5d5d0466a8ce04b2325c7fc19597cce0d', 'iPhone', '525273aff48e1f0333654de21b089ede', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-10 10:11:15', 1, NULL),
(22, 279, 'REG_279_20250710_161414', 'e621ae5b3a6a9b089170c205975183cc1f3bd2b9187afbd45921b5924c17fc1b', 'Android 10', 'c255df52fb73fbd32aa89072228ce41c', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 10:14:14', 1, NULL),
(23, 275, 'REG_275_20250710_162321', '439667666b5f830bd405a2d62a61c8bce6bb201718229ee464c2eba2b729c8e5', 'Android 10', '3dcecf20b345ff13d512a7f899c53b06', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 10:23:21', 1, NULL),
(24, 247, 'REG_247_20250710_162610', 'acf4a49fe2b31b1dfb4d7159919f289419dc221b32a1a377ed8c6b1ae58c52f7', 'iPhone', '34bfa69a2f09c6c5dfc739614e2230e0', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/137.0.7151.107 Mobile/15E148 Safari/604.1', '2025-07-10 10:26:10', 1, NULL),
(25, 252, 'REG_252_20250710_162911', '81fe305b1e027f9c518a993c03ea321ad4b74041c193779b37542ce941122850', 'Android 10', '10483ce41458e09456db40d3ea74843c', '************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36', '2025-07-10 10:29:11', 1, NULL),
(26, 225, 'REG_225_20250710_163037', 'c24e9190e10f67588985f6d29c9c6d59fed15af13612de611f66135b5db7504e', 'Android 10', '69656bf4263da564d2fa1d2d024d115d', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 10:30:37', 1, NULL),
(27, 226, 'REG_226_20250710_163133', 'c1525b2536289a381a4e3bbb69f9472752e6a3b2664d9fba58f9524d17e736ee', 'Android 10', '5c5ac848b63fd860127de5ed4f2ba507', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 10:31:33', 1, NULL),
(28, 268, 'REG_268_20250710_163722', 'f481c4a90340d12e4468b167201097189b84b0b6334cf782dc964c70c1dbd48c', 'iPhone', 'e8d48daba2d59949e6ecf22dbc2bca39', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-10 10:37:22', 1, NULL),
(29, 296, 'REG_296_20250710_163942', '732569a9ad1971eaf2830620277e89a55d9767539f9428ea7cd3da1ab1fe6d3e', 'Android 10', 'decc86ee1d470131f364bc6a839365a4', '************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 10:39:42', 1, NULL),
(30, 233, 'REG_233_20250710_164104', '723a13de74131482e78da89f903848169ee0000ff22197e2ab05a017025acc62', 'Android 10', 'c645fa608540a6c45d2569892220244a', '************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 10:41:04', 1, NULL),
(31, 253, 'REG_253_20250710_164301', '6549a6b219e70f6cd7b649fdb1f1dcd4c17be0cad3de160eb7afd0dc2b9def38', 'iPhone', '2daf14118043e1976a37f94899648d68', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/137.0.7151.107 Mobile/15E148 Safari/604.1', '2025-07-10 10:43:01', 1, NULL),
(32, 256, 'REG_256_20250710_201533', '295199dfd8696eb86c0e2e336a1d8efa1f9cd9f94486453844ba9d9bf0578b15', 'Android 10', '0e69c45a05c864c8092ed3eccffd38c8', '************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 14:15:33', 1, '2025-07-10 14:16:02'),
(33, 240, 'REG_240_20250710_202245', '19385039b0762c1ba19c9ccdb456efef72244deabb5eb384baac4c5b1e66ad3a', 'Android 10', '4d1ef27d579214c555758574e2f001d5', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 14:22:45', 1, NULL),
(34, 293, 'REG_293_20250710_202516', '81d61ae52fabe333aeccad017d14c6cbd5f4d46636174e676c21ff02e2b5f354', 'iPhone', '9ca96315b8101c500273d1ba5d2d7b86', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 14:25:16', 1, NULL),
(35, 269, 'REG_269_20250710_202629', '147c43eedf1ae8a0a9c36275ae40c7a7a6a49fa9ffe832bf9edf13ddab8fa8b8', 'Android 10', '797a885ce599f1779d9473eaf84f9f3f', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 14:26:29', 1, NULL),
(36, 261, 'REG_261_20250710_202729', 'e03753aa7cc1a05c420c06c7c91f6fa81f2a1714ea863dccdcfd2d3b39cf08d0', 'iPhone', 'c29c74c9143ecb56ba4bca6a532089b8', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 14:27:29', 1, NULL),
(37, 295, 'REG_295_20250710_202820', '9d274489a7f5ff12b5232f737bffd03beea7113b7ec730b7859ee398e8db7f34', 'Android 10', '586bd2cdb2ad746e7c8df744ed39ee7a', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36', '2025-07-10 14:28:20', 1, NULL),
(38, 232, 'REG_232_20250710_203256', '2f350bf103dc7ee87992b8dea72902b9ace7a986d572c018bb2bc3e7154c5f64', 'Android 10', 'af3500d30b1bc78f933f0009b018d770', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 14:32:56', 1, NULL),
(39, 224, 'REG_224_20250710_204357', '0d586d2bcb34fc5a9ffbb893b8735a67922d4df88b365a08567467a0d9a0abb0', 'Android 10', '124ec7ef623b659d0d003a237b6de63e', '************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/28.0 Chrome/130.0.0.0 Mobile Safari/537.36', '2025-07-10 14:43:57', 1, NULL),
(40, 236, 'REG_236_20250710_204432', '566dcfc0a54d6691fa546b779be69c2655f0b407aac0e744545b1b58d373d47d', 'Android 10', '81d384e50055d560d17e5c199ca48325', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 14:44:32', 1, '2025-07-10 14:45:19'),
(41, 272, 'REG_272_20250710_204623', 'b846269c524e3d634b467228f706a2572e2a5f63db96fce1727850a934a753e3', 'iPhone', '080ae1ed6b5bc6b8569080e9e4b8dcb0', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1', '2025-07-10 14:46:23', 1, NULL),
(42, 249, 'REG_249_20250710_204735', '0fc286ffcec674d5b2d28a577350b99b9289a45ea0c0be80678ee0bb4929b8e2', 'Android 10', '38c408f29354d4c0c62f75c202e1fcc0', '************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-10 14:47:35', 1, NULL),
(43, 290, 'REG_290_20250710_205049', '61bca76320b663595133b2443f254522bc10e3b0cc47dd82d2f00bc3bd8957f3', 'iPhone', '4b63ad078adc58d168ffb7588f4b35bf', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 14:50:49', 1, NULL),
(44, 290, 'REG_290_20250710_205605', 'f9b3ca1578424c5cbd147bce124a1705ada1c186aeecf3b28ddf2d667a7cb36a', 'iPhone', '4b63ad078adc58d168ffb7588f4b35bf', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 14:56:05', 1, NULL),
(45, 290, 'REG_290_20250710_210159', '48b5449423611631f733fb02d68c4b1c97c939d6789b1fbb699ce67c0e7ad5da', 'iPhone', '3124c1255184cd63ce508a75a57f68cc', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 15:01:59', 1, NULL),
(46, 290, 'REG_290_20250710_210634', '021b29253845740dbc8f4ab850842194019ea6d997d21b976d1b153f983d0d14', 'iPhone', 'c817415f506e2f5b8f10304becca74ef', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 15:06:34', 1, NULL),
(47, 290, 'REG_290_20250710_210821', 'f12bd89770dd7d123d5855eae462955f5209a736a905319b91917d86c53aa5fe', 'iPhone', 'c817415f506e2f5b8f10304becca74ef', '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1', '2025-07-10 15:08:21', 1, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `device_registrations`
--
ALTER TABLE `device_registrations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `registration_id` (`registration_id`),
  ADD UNIQUE KEY `device_token` (`device_token`),
  ADD KEY `staff_id` (`staff_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `device_registrations`
--
ALTER TABLE `device_registrations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=48;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `device_registrations`
--
ALTER TABLE `device_registrations`
  ADD CONSTRAINT `device_registrations_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
