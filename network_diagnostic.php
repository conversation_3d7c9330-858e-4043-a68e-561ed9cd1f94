<?php
// Enhanced Network Diagnostic Tool for NCHM Attendance System
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database config for IP validation
require_once 'config/database.php';

$db = new Database();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCHM Attendance System - Network Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-good { color: #27ae60; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .status-error { color: #e74c3c; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .refresh-btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0; }
        .refresh-btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 NCHM Attendance System - Network Diagnostic</h1>
        <p><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Diagnostics</button>

        <!-- Current Connection Information -->
        <div class="section">
            <h2>📡 Current Connection Information</h2>
            <table>
                <tr><th>Parameter</th><th>Value</th><th>Status</th></tr>
                <tr>
                    <td>Server IP</td>
                    <td><?php echo $_SERVER['SERVER_ADDR'] ?? 'Not available'; ?></td>
                    <td><?php echo ($_SERVER['SERVER_ADDR'] === '*************') ? '<span class="status-good">✅ Correct</span>' : '<span class="status-warning">⚠️ Check IP</span>'; ?></td>
                </tr>
                <tr>
                    <td>Client IP</td>
                    <td><?php echo $_SERVER['REMOTE_ADDR'] ?? 'Not available'; ?></td>
                    <td><?php 
                        $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
                        if ($db->validateIPRange($client_ip)) {
                            echo '<span class="status-good">✅ Allowed Range</span>';
                        } else {
                            echo '<span class="status-error">❌ Not in Allowed Range</span>';
                        }
                    ?></td>
                </tr>
                <tr>
                    <td>Host Header</td>
                    <td><?php echo $_SERVER['HTTP_HOST'] ?? 'Not available'; ?></td>
                    <td><?php echo (strpos($_SERVER['HTTP_HOST'] ?? '', '*************') !== false) ? '<span class="status-good">✅ Network Access</span>' : '<span class="status-warning">⚠️ Local Access</span>'; ?></td>
                </tr>
                <tr>
                    <td>User Agent</td>
                    <td><?php echo substr($_SERVER['HTTP_USER_AGENT'] ?? 'Not available', 0, 100); ?></td>
                    <td><?php 
                        $ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
                        if (strpos($ua, 'Mobile') !== false || strpos($ua, 'Android') !== false || strpos($ua, 'iPhone') !== false) {
                            echo '<span class="status-good">📱 Mobile Device</span>';
                        } else {
                            echo '<span class="status-warning">💻 Desktop</span>';
                        }
                    ?></td>
                </tr>
            </table>
        </div>

        <!-- Network Connectivity Tests -->
        <div class="section">
            <h2>🌐 Network Connectivity Tests</h2>
            
            <?php
            // Test 1: Server can resolve its own hostname
            $hostname = gethostname();
            $host_ip = gethostbyname($hostname);
            ?>
            <div class="test-result <?php echo ($host_ip !== $hostname) ? 'success' : 'warning'; ?>">
                <strong>DNS Resolution Test:</strong> 
                Hostname: <?php echo $hostname; ?> → IP: <?php echo $host_ip; ?>
                <?php echo ($host_ip !== $hostname) ? '✅ Working' : '⚠️ DNS Issue'; ?>
            </div>

            <!-- Access URLs Testing -->
            <h3>📍 Access URL Tests</h3>
            <table>
                <tr><th>URL</th><th>Purpose</th><th>Status</th></tr>
                <tr>
                    <td><a href="http://localhost:8080/attendance/" target="_blank">http://localhost:8080/attendance/</a></td>
                    <td>Local Development</td>
                    <td><span class="status-good">✅ Should work from server</span></td>
                </tr>
                <tr>
                    <td><a href="http://*************:8080/attendance/" target="_blank">http://*************:8080/attendance/</a></td>
                    <td>Network Access (Staff)</td>
                    <td><span class="status-good">✅ Primary URL for staff</span></td>
                </tr>
            </table>
        </div>

        <!-- IP Range Validation -->
        <div class="section">
            <h2>🔒 IP Range Validation</h2>
            <?php
            $test_ips = [
                '*************' => 'Server IP',
                '***********' => 'Staff Network Start',
                '***********00' => 'Staff Network Middle',
                '*************' => 'Staff Network End',
                '127.0.0.1' => 'Localhost',
                '*************' => 'Test Local Network'
            ];
            
            echo '<table>';
            echo '<tr><th>IP Address</th><th>Description</th><th>Access Status</th></tr>';
            
            foreach ($test_ips as $ip => $desc) {
                $allowed = $db->validateIPRange($ip);
                $status_class = $allowed ? 'status-good' : 'status-error';
                $status_text = $allowed ? '✅ Allowed' : '❌ Blocked';
                echo "<tr><td>$ip</td><td>$desc</td><td><span class='$status_class'>$status_text</span></td></tr>";
            }
            echo '</table>';
            ?>
        </div>

        <!-- System Status -->
        <div class="section">
            <h2>⚙️ System Status</h2>
            
            <?php
            // Check database connection
            try {
                $conn = $db->getConnection();
                $db_status = $conn ? '✅ Connected' : '❌ Failed';
                $db_class = $conn ? 'success' : 'error';
            } catch (Exception $e) {
                $db_status = '❌ Error: ' . $e->getMessage();
                $db_class = 'error';
            }
            ?>
            
            <div class="test-result <?php echo $db_class; ?>">
                <strong>Database Connection:</strong> <?php echo $db_status; ?>
            </div>

            <?php
            // Check QR code directory
            $qr_dir = 'qr_codes';
            $qr_writable = is_writable($qr_dir);
            ?>
            <div class="test-result <?php echo $qr_writable ? 'success' : 'error'; ?>">
                <strong>QR Code Directory:</strong> <?php echo $qr_writable ? '✅ Writable' : '❌ Not writable'; ?>
                (<?php echo $qr_dir; ?>)
            </div>

            <?php
            // Check PHP extensions
            $required_extensions = ['pdo', 'pdo_mysql', 'gd', 'curl'];
            echo '<h3>📦 PHP Extensions</h3>';
            echo '<table>';
            echo '<tr><th>Extension</th><th>Status</th></tr>';
            
            foreach ($required_extensions as $ext) {
                $loaded = extension_loaded($ext);
                $status_class = $loaded ? 'status-good' : 'status-error';
                $status_text = $loaded ? '✅ Loaded' : '❌ Missing';
                echo "<tr><td>$ext</td><td><span class='$status_class'>$status_text</span></td></tr>";
            }
            echo '</table>';
            ?>
        </div>

        <!-- Troubleshooting Guide -->
        <div class="section">
            <h2>🔧 Troubleshooting Guide</h2>

            <h3>If staff can't access from phones (172.31.18.x network):</h3>
            <ol>
                <li><strong>Check Network Connectivity:</strong>
                    <ul>
                        <li>Ensure staff phones are connected to office Wi-Fi</li>
                        <li>Verify phones can ping *************</li>
                        <li>Check if router allows communication between 172.31.18.x and *************</li>
                    </ul>
                </li>

                <li><strong>Windows Firewall:</strong>
                    <ul>
                        <li>Ensure port 8080 is allowed through Windows Firewall</li>
                        <li>Check if "Laragon 8080" rule is enabled</li>
                    </ul>
                </li>

                <li><strong>Apache Configuration:</strong>
                    <ul>
                        <li>Verify Apache is listening on 0.0.0.0:8080 (not just 127.0.0.1)</li>
                        <li>Check virtual host configuration allows network access</li>
                    </ul>
                </li>

                <li><strong>Network Router/Switch:</strong>
                    <ul>
                        <li>Ensure VLAN or subnet routing allows 172.31.18.x → *************</li>
                        <li>Check for any access control lists (ACLs) blocking traffic</li>
                    </ul>
                </li>
            </ol>

            <h3>Quick Tests for Staff:</h3>
            <ul>
                <li><strong>URL to try:</strong> http://*************:8080/attendance/</li>
                <li><strong>Alternative:</strong> Try accessing this diagnostic page: http://*************:8080/attendance/network_diagnostic.php</li>
                <li><strong>Network test:</strong> Can you ping ************* from your phone?</li>
            </ul>
        </div>

        <!-- Quick Actions -->
        <div class="section">
            <h2>⚡ Quick Actions</h2>
            <p>
                <a href="index.php" class="refresh-btn">🏠 Main Attendance Page</a>
                <a href="admin/simple_login.php" class="refresh-btn">🔐 Admin Login</a>
                <a href="generate_qr.php?type=attendance" class="refresh-btn">📱 Test QR Generation</a>
            </p>
        </div>

        <div class="section">
            <h2>📊 Server Environment</h2>
            <table>
                <tr><th>Parameter</th><th>Value</th></tr>
                <tr><td>PHP Version</td><td><?php echo PHP_VERSION; ?></td></tr>
                <tr><td>Server Software</td><td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td></tr>
                <tr><td>Document Root</td><td><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></td></tr>
                <tr><td>Current Time</td><td><?php echo date('Y-m-d H:i:s T'); ?></td></tr>
            </table>
        </div>
    </div>

    <script>
        // Auto-refresh every 30 seconds if requested
        if (window.location.search.includes('auto=1')) {
            setTimeout(() => location.reload(), 30000);
        }

        // Add click handler for auto-refresh
        document.querySelector('.refresh-btn').addEventListener('click', function() {
            this.textContent = '🔄 Refreshing...';
            this.disabled = true;
        });
    </script>
</body>
</html>
