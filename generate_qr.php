<?php
// QR Code Generation API
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

try {
    require_once 'classes/DatabaseManager.php';
    require_once 'classes/QRCodeManager.php';

    $db = new DatabaseManager();
    $qr_manager = new QRCodeManager();

    // Get request type
    $type = $_GET['type'] ?? '';

    if ($type === 'register') {
        // Generate registration QR code (generic for any staff)
        $registration_code = 'REG_' . date('Ymd_His') . '_' . bin2hex(random_bytes(4));
        
        // Create QR data with full URL
        $host = $_SERVER['HTTP_HOST'];
        // Remove port from HTTP_HOST if it exists, then add the correct port
        $host_parts = explode(':', $host);
        $clean_host = $host_parts[0];
        $port = $_SERVER['SERVER_PORT'];

        if ($port != 80 && $port != 443) {
            $base_url = 'http://' . $clean_host . ':' . $port;
        } else {
            $base_url = 'http://' . $clean_host;
        }

        $registration_url = $base_url . '/register.php?code=' . $registration_code;

        // QR code should contain just the URL for easy scanning
        $qr_data = $registration_url;

        // Store QR code in database (7 days expiry, unlimited usage for registration)
        $db->createQRCode($registration_code, 'registration', null, 24 * 7, null);

        // Generate QR code image using external service directly
        $filename = 'qr_codes/registration_' . date('Ymd_His') . '.png';

        if (!is_dir('qr_codes')) {
            mkdir('qr_codes', 0755, true);
        }

        // Use QR Server API directly for better reliability
        $qr_service_url = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($qr_data);

        $context = stream_context_create([
            'http' => [
                'timeout' => 15,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'method' => 'GET'
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $qr_image_data = @file_get_contents($qr_service_url, false, $context);

        if ($qr_image_data !== false && strlen($qr_image_data) > 100) {
            file_put_contents($filename, $qr_image_data);
        } else {
            // Fallback to QR manager
            $qr_manager->createSimpleQRCode($qr_data, $filename);
        }

        echo json_encode([
            'success' => true,
            'qr_data' => [
                'code' => $registration_code,
                'image_path' => $filename,
                'expires' => date('M j, Y - H:i', strtotime('+7 days')),
                'type' => 'registration'
            ]
        ]);

    } elseif ($type === 'attendance') {
        // Generate attendance QR code that shows IP link
        $attendance_code = 'ATT_' . date('Ymd_His') . '_' . bin2hex(random_bytes(4));

        // Create QR data with IP link text (not direct URL)
        $host = $_SERVER['HTTP_HOST'];
        // Remove port from HTTP_HOST if it exists, then add the correct port
        $host_parts = explode(':', $host);
        $clean_host = $host_parts[0];
        $port = $_SERVER['SERVER_PORT'];

        if ($port != 80 && $port != 443) {
            $base_url = 'http://' . $clean_host . ':' . $port;
        } else {
            $base_url = 'http://' . $clean_host;
        }

        $attendance_url = $base_url . '/attendance.php?code=' . $attendance_code;

        // QR code contains the IP link that users can click
        $qr_data = $attendance_url;

        // Store QR code in database (24 hours expiry, unlimited usage)
        $db->createQRCode($attendance_code, 'daily_attendance', null, 24, null);

        // Generate QR code image using external service directly
        $filename = 'qr_codes/attendance_' . date('Ymd_His') . '.png';

        if (!is_dir('qr_codes')) {
            mkdir('qr_codes', 0755, true);
        }

        // Use QR Server API directly for better reliability
        $qr_service_url = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($qr_data);

        $context = stream_context_create([
            'http' => [
                'timeout' => 15,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'method' => 'GET'
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $qr_image_data = @file_get_contents($qr_service_url, false, $context);

        if ($qr_image_data !== false && strlen($qr_image_data) > 100) {
            file_put_contents($filename, $qr_image_data);
        } else {
            // Fallback to QR manager
            $qr_manager->createSimpleQRCode($qr_data, $filename);
        }

        echo json_encode([
            'success' => true,
            'qr_data' => [
                'code' => $attendance_code,
                'image_path' => $filename,
                'expires' => date('M j, Y - H:i', strtotime('+24 hours')),
                'type' => 'attendance'
            ]
        ]);

    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid QR code type specified'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error generating QR code: ' . $e->getMessage()
    ]);
}
?>
