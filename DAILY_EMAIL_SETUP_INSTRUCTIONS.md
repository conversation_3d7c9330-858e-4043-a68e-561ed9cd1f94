# 📧 Daily Attendance Email Setup Instructions

This guide will help you set up automated daily attendance reports that will be sent to `<EMAIL>` and `<EMAIL>` every day at 8:00 PM.

## 📋 Files Created

1. **email_config.php** - Email configuration settings
2. **daily_report_sender.php** - Main script that generates and sends reports
3. **run_daily_report.bat** - Windows batch file for Task Scheduler
4. **DAILY_EMAIL_SETUP_INSTRUCTIONS.md** - This instruction file

## 🔧 Step 1: Configure Email Settings

1. Open `email_config.php` in a text editor
2. Update the SMTP settings with your email provider details:

```php
'smtp' => [
    'host' => 'smtp.gmail.com',  // Your SMTP server
    'port' => 587,               // SMTP port
    'encryption' => 'tls',       // Encryption type
    'username' => '<EMAIL>',  // Your email
    'password' => 'your-app-password',     // Your password/app password
    'from_email' => '<EMAIL>',
    'from_name' => 'NCHM Attendance System'
],
```

### For Gmail Users:
- Use `smtp.gmail.com` as host
- Port `587` with `tls` encryption
- Enable 2-factor authentication
- Generate an "App Password" instead of using your regular password
- Go to: Google Account → Security → 2-Step Verification → App passwords

## 🔧 Step 2: Test the Email Script

1. Open Command Prompt as Administrator
2. Navigate to your attendance folder:
   ```cmd
   cd C:\laragon\www\attendance
   ```
3. Run the script manually to test:
   ```cmd
   C:\laragon\bin\php\php-8.1.10-Win32-vs16-x64\php.exe daily_report_sender.php
   ```
4. Check the `logs` folder for any error messages
5. Verify that emails are received at the specified addresses

## 🔧 Step 3: Set Up Windows Task Scheduler

### Method 1: Using Task Scheduler GUI

1. **Open Task Scheduler:**
   - Press `Win + R`, type `taskschd.msc`, press Enter
   - Or search "Task Scheduler" in Start menu

2. **Create Basic Task:**
   - Click "Create Basic Task..." in the right panel
   - Name: `NCHM Daily Attendance Email`
   - Description: `Send daily attendance reports at 8:00 PM`
   - Click "Next"

3. **Set Trigger:**
   - Select "Daily"
   - Click "Next"
   - Start date: Today's date
   - Start time: `8:00:00 PM`
   - Recur every: `1` days
   - Click "Next"

4. **Set Action:**
   - Select "Start a program"
   - Click "Next"
   - Program/script: Browse and select `C:\laragon\www\attendance\run_daily_report.bat`
   - Start in: `C:\laragon\www\attendance`
   - Click "Next"

5. **Finish:**
   - Review settings
   - Check "Open the Properties dialog..."
   - Click "Finish"

6. **Configure Advanced Settings:**
   - In Properties dialog, go to "General" tab
   - Check "Run whether user is logged on or not"
   - Check "Run with highest privileges"
   - Go to "Settings" tab
   - Check "Run task as soon as possible after a scheduled start is missed"
   - Click "OK"

### Method 2: Using Command Line

Run this command as Administrator:

```cmd
schtasks /create /tn "NCHM Daily Attendance Email" /tr "C:\laragon\www\attendance\run_daily_report.bat" /sc daily /st 20:00 /ru SYSTEM
```

## 🔧 Step 4: Verify Setup

1. **Check Task Scheduler:**
   - Open Task Scheduler
   - Look for "NCHM Daily Attendance Email" in the task list
   - Right-click and select "Run" to test immediately

2. **Monitor Logs:**
   - Check `logs/scheduler.log` for batch file execution logs
   - Check `logs/daily_email_YYYY-MM.log` for PHP script logs

3. **Test Email Delivery:**
   - Run the task manually first
   - Verify emails are received
   - Check spam/junk folders if not received

## 📊 What Gets Sent

The daily email includes:

- **HTML Email Body:**
  - NCHM header with date
  - Summary statistics (Total, Present, Complete, Absent)
  - Attendance rate percentage
  - Professional formatting

- **Excel Attachment:**
  - Complete attendance data for the day
  - Staff names, EID/CID/Permit, positions, divisions
  - Clock in/out times and status
  - Summary statistics at the top

## 🔍 Troubleshooting

### Email Not Sending:
1. Check SMTP settings in `email_config.php`
2. Verify email credentials and app passwords
3. Check `logs/daily_email_*.log` for error messages
4. Test with a simple email first

### Task Not Running:
1. Verify Task Scheduler service is running
2. Check task properties and triggers
3. Run batch file manually to test
4. Check Windows Event Viewer for task scheduler errors

### PHP Errors:
1. Verify PHP path in batch file matches your Laragon installation
2. Check database connection settings
3. Ensure all required folders exist (logs, temp)

## 📁 File Structure

```
C:\laragon\www\attendance\
├── email_config.php              # Email configuration
├── daily_report_sender.php       # Main email script
├── run_daily_report.bat          # Batch file for scheduler
├── logs/                         # Log files directory
│   ├── scheduler.log             # Batch execution logs
│   └── daily_email_YYYY-MM.log   # PHP script logs
└── temp/                         # Temporary Excel files
```

## ⚠️ Important Notes

- **No Changes to Existing System:** All files are separate and don't modify your working attendance system
- **Email Security:** Use app passwords, not regular passwords for Gmail
- **Time Zone:** Set to Asia/Thimphu (Bhutan time)
- **Backup:** Keep backups of configuration files
- **Testing:** Always test manually before relying on automation

## 📞 Support

If you encounter issues:
1. Check the log files first
2. Test each component separately
3. Verify all paths and credentials
4. Ensure Laragon and services are running

The system will automatically send daily reports at 8:00 PM Bhutan time to both specified email addresses with detailed attendance data in Excel format.
