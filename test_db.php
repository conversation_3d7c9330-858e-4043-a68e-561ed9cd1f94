<?php
echo "<h1>🔍 Database Connection Test</h1>";

try {
    require_once 'config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "<p style='color: green;'>✅ <strong>Database Connected Successfully!</strong></p>";
        
        // Test basic query
        $stmt = $conn->query("SELECT COUNT(*) as count FROM staff");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>📊 <strong>Staff Records:</strong> " . $result['count'] . "</p>";
        
        // Test QR codes table
        $stmt = $conn->query("SELECT COUNT(*) as count FROM qr_codes");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>🔗 <strong>QR Codes:</strong> " . $result['count'] . "</p>";
        
        echo "<p>🌐 <strong>Server IP:</strong> " . ($_SERVER['SERVER_ADDR'] ?? 'Not detected') . "</p>";
        echo "<p>🔗 <strong>Access URL:</strong> http://" . ($_SERVER['HTTP_HOST'] ?? 'localhost') . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ <strong>Database Connection Failed!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>🔗 Quick Links</h2>";
echo "<a href='index.php'>Main Attendance Page</a><br>";
echo "<a href='admin/simple_login.php'>Admin Login</a><br>";
echo "<a href='network_setup.php'>Network Setup</a>";
?>
