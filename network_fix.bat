@echo off
echo ========================================
echo NCHM Attendance System - Network Fix
echo ========================================
echo.

echo Checking current network configuration...
echo.

echo 1. Current IP Configuration:
ipconfig | findstr "IPv4"
echo.

echo 2. Checking if Apache is listening on port 8080:
netstat -an | findstr :8080
echo.

echo 3. Testing local connectivity:
echo Testing localhost:8080...
powershell -Command "Test-NetConnection -ComputerName localhost -Port 8080 | Select-Object TcpTestSucceeded"

echo Testing network IP *************:8080...
powershell -Command "Test-NetConnection -ComputerName ************* -Port 8080 | Select-Object TcpTestSucceeded"
echo.

echo 4. Checking Windows Firewall rules for port 8080:
netsh advfirewall firewall show rule name=all | findstr "8080"
echo.

echo 5. Checking if Laragon services are running:
tasklist | findstr "httpd.exe"
tasklist | findstr "mysqld.exe"
echo.

echo ========================================
echo POTENTIAL FIXES:
echo ========================================
echo.

echo If Apache is not listening on 0.0.0.0:8080:
echo - Restart Laragon
echo - Check Apache configuration
echo.

echo If firewall is blocking:
echo - Run: netsh advfirewall firewall add rule name="Laragon 8080" dir=in action=allow protocol=TCP localport=8080
echo.

echo If network routing issues:
echo - Check router/switch configuration
echo - Ensure 172.31.18.x can reach *************
echo.

echo ========================================
echo QUICK TESTS FOR STAFF:
echo ========================================
echo.
echo 1. Staff should try: http://*************:8080/attendance/mobile_test.php
echo 2. If that works, try: http://*************:8080/attendance/
echo 3. For full diagnostics: http://*************:8080/attendance/network_diagnostic.php
echo.

pause
