# Apache Virtual Host Configuration for NCHM Attendance System
# Save this as: C:\laragon\etc\apache2\sites-enabled\attendance.conf

<VirtualHost *:8080>
    DocumentRoot "C:/laragon/www/attendance"
    ServerName localhost
    ServerAlias *************
    
    <Directory "C:/laragon/www/attendance">
        AllowOverride All
        Require all granted
        # Allow office network
        Require ip ***********/24
        Require ip *************
        Require ip 127.0.0.1
        Require ip ::1
    </Directory>
    
    # Enable PHP
    <FilesMatch \.php$>
        SetHandler application/x-httpd-php
    </FilesMatch>
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Logging
    ErrorLog "C:/laragon/etc/apache2/logs/attendance_error.log"
    CustomLog "C:/laragon/etc/apache2/logs/attendance_access.log" combined
</VirtualHost>

# Alternative configuration if you want to bind specifically to your IP
<VirtualHost *************:8080>
    DocumentRoot "C:/laragon/www/attendance"
    ServerName *************
    
    <Directory "C:/laragon/www/attendance">
        AllowOverride All
        Require all granted
    </Directory>
    
    <FilesMatch \.php$>
        SetHandler application/x-httpd-php
    </FilesMatch>
</VirtualHost>
