<?php
session_start();
require_once '../classes/DatabaseManager.php';
require_once '../classes/QRCodeManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

$db = new DatabaseManager();
$qr_manager = new QRCodeManager();

$message = '';
$error = '';

// Handle staff actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['generate_registration_qr']) && isset($_POST['staff_id'])) {
        $staff_id = $_POST['staff_id'];
        try {
            $registration_qr = $qr_manager->generateRegistrationQR($staff_id);
            $message = "Registration QR code generated successfully for staff ID: $staff_id";
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if (isset($_POST['add_staff'])) {
        // Add new staff member
        $name = trim($_POST['name']);
        $eid_cid_permit = trim($_POST['eid_cid_permit']);
        $id_type = $_POST['id_type'];
        $position_title = trim($_POST['position_title']);
        $division = trim($_POST['division']);
        
        if (empty($name) || empty($eid_cid_permit) || empty($position_title) || empty($division)) {
            $error = "All fields are required";
        } else {
            try {
                $stmt = $db->conn->prepare("INSERT INTO staff (name, eid_cid_permit, id_type, position_title, division) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$name, $eid_cid_permit, $id_type, $position_title, $division]);
                $message = "Staff member added successfully";
                
                // Log action
                $db->logAction('admin_action', 'Staff member added', 
                    json_encode(['name' => $name, 'eid_cid_permit' => $eid_cid_permit]),
                    null, $_SESSION['admin_id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) {
                    $error = "EID/CID/Permit already exists";
                } else {
                    $error = "Error adding staff member: " . $e->getMessage();
                }
            }
        }
    }
    
    if (isset($_POST['update_staff'])) {
        // Update staff member
        $staff_id = $_POST['staff_id'];
        $name = trim($_POST['name']);
        $position_title = trim($_POST['position_title']);
        $division = trim($_POST['division']);
        
        try {
            $stmt = $db->conn->prepare("UPDATE staff SET name = ?, position_title = ?, division = ? WHERE id = ?");
            $stmt->execute([$name, $position_title, $division, $staff_id]);
            $message = "Staff member updated successfully";
            
            // Log action
            $db->logAction('admin_action', 'Staff member updated', 
                json_encode(['staff_id' => $staff_id, 'name' => $name]),
                null, $_SESSION['admin_id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
        } catch (PDOException $e) {
            $error = "Error updating staff member: " . $e->getMessage();
        }
    }
    
    if (isset($_POST['delete_staff']) && $_SESSION['admin_role'] === 'super_admin') {
        // Delete staff member (Super Admin only)
        $staff_id = $_POST['staff_id'];
        try {
            $staff = $db->getStaffById($staff_id);
            $stmt = $db->conn->prepare("DELETE FROM staff WHERE id = ?");
            $stmt->execute([$staff_id]);
            $message = "Staff member deleted successfully";
            
            // Log action
            $db->logAction('admin_action', 'Staff member deleted', 
                json_encode(['staff_id' => $staff_id, 'name' => $staff['name']]),
                null, $_SESSION['admin_id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
        } catch (PDOException $e) {
            $error = "Error deleting staff member: " . $e->getMessage();
        }
    }
}

// Handle Excel export
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    $staff_list = $db->getAllStaff();
    
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="staff_list_' . date('Y-m-d') . '.xls"');
    header('Cache-Control: max-age=0');
    
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>EID/CID/Permit</th><th>ID Type</th><th>Position</th><th>Division</th><th>Registered</th><th>Registration Date</th></tr>";
    
    foreach ($staff_list as $staff) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($staff['id']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['name']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['eid_cid_permit']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['id_type']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['position_title']) . "</td>";
        echo "<td>" . htmlspecialchars($staff['division']) . "</td>";
        echo "<td>" . ($staff['is_registered'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . htmlspecialchars($staff['registered_date']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    exit;
}

// Get all staff
$staff_list = $db->getAllStaff();

// Get staff for editing
$edit_staff = null;
if (isset($_GET['edit'])) {
    $edit_staff = $db->getStaffById($_GET['edit']);
}

$page_title = 'Staff Management';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - DHM Admin</title>
    <link rel="stylesheet" href="../assets/admin-style.css">
    <style>
        .staff-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            margin-top: 2rem;
        }

        .staff-form {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }

        .staff-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-box {
            padding: 0.5rem;
            border: 1px solid #e1e8ed;
            border-radius: 5px;
            width: 300px;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
        }

        .registration-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }

        .status-registered {
            background: #28a745;
        }

        .status-unregistered {
            background: #dc3545;
        }

        @media (max-width: 1024px) {
            .staff-grid {
                grid-template-columns: 1fr;
            }
        }

        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .staff-table {
                box-shadow: none !important;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    <?php include 'includes/navigation.php'; ?>

    <div class="main-content">
        <div class="page-header">
            <h1><?php echo $page_title; ?></h1>
            <div class="page-actions no-print">
                <button onclick="window.print()" class="btn">🖨️ Print</button>
                <a href="?export=excel" class="btn btn-success">📊 Export Excel</a>
            </div>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <div class="staff-grid">
            <div class="staff-table">
                <div class="table-header">
                    <h3>Staff List (<?php echo count($staff_list); ?> members)</h3>
                    <input type="text" id="searchBox" class="search-box" placeholder="Search staff...">
                </div>

                <div style="overflow-x: auto;">
                    <table class="table" id="staffTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>EID/CID/Permit</th>
                                <th>Position</th>
                                <th>Division</th>
                                <th>Status</th>
                                <th class="no-print">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($staff_list as $staff): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($staff['name']); ?></td>
                                <td><?php echo htmlspecialchars($staff['eid_cid_permit']); ?></td>
                                <td><?php echo htmlspecialchars($staff['position_title']); ?></td>
                                <td><?php echo htmlspecialchars($staff['division']); ?></td>
                                <td>
                                    <div class="registration-status">
                                        <div class="status-indicator <?php echo $staff['is_registered'] ? 'status-registered' : 'status-unregistered'; ?>"></div>
                                        <?php echo $staff['is_registered'] ? 'Registered' : 'Not Registered'; ?>
                                    </div>
                                </td>
                                <td class="no-print">
                                    <div class="action-buttons">
                                        <a href="?edit=<?php echo $staff['id']; ?>" class="btn btn-sm">Edit</a>
                                        <?php if (!$staff['is_registered']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="staff_id" value="<?php echo $staff['id']; ?>">
                                                <button type="submit" name="generate_registration_qr" class="btn btn-sm btn-success">QR Code</button>
                                            </form>
                                        <?php endif; ?>
                                        <?php if ($_SESSION['admin_role'] === 'super_admin'): ?>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this staff member?')">
                                                <input type="hidden" name="staff_id" value="<?php echo $staff['id']; ?>">
                                                <button type="submit" name="delete_staff" class="btn btn-sm btn-danger">Delete</button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="staff-form no-print">
                <h3><?php echo $edit_staff ? 'Edit Staff Member' : 'Add New Staff Member'; ?></h3>

                <form method="POST">
                    <?php if ($edit_staff): ?>
                        <input type="hidden" name="staff_id" value="<?php echo $edit_staff['id']; ?>">
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="name">Full Name:</label>
                        <input type="text" id="name" name="name" class="form-control" required
                               value="<?php echo $edit_staff ? htmlspecialchars($edit_staff['name']) : ''; ?>">
                    </div>

                    <?php if (!$edit_staff): ?>
                    <div class="form-group">
                        <label for="eid_cid_permit">EID/CID/Permit:</label>
                        <input type="text" id="eid_cid_permit" name="eid_cid_permit" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="id_type">ID Type:</label>
                        <select id="id_type" name="id_type" class="form-control" required>
                            <option value="">Select ID Type</option>
                            <option value="EID">EID</option>
                            <option value="CID">CID</option>
                            <option value="Permit">Permit</option>
                        </select>
                    </div>
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="position_title">Position Title:</label>
                        <input type="text" id="position_title" name="position_title" class="form-control" required
                               value="<?php echo $edit_staff ? htmlspecialchars($edit_staff['position_title']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="division">Division:</label>
                        <select id="division" name="division" class="form-control" required>
                            <option value="">Select Division</option>
                            <option value="HWRSD" <?php echo ($edit_staff && $edit_staff['division'] === 'HWRSD') ? 'selected' : ''; ?>>HWRSD</option>
                            <option value="MSD" <?php echo ($edit_staff && $edit_staff['division'] === 'MSD') ? 'selected' : ''; ?>>MSD</option>
                            <option value="CSD" <?php echo ($edit_staff && $edit_staff['division'] === 'CSD') ? 'selected' : ''; ?>>CSD</option>
                            <option value="TSRD" <?php echo ($edit_staff && $edit_staff['division'] === 'TSRD') ? 'selected' : ''; ?>>TSRD</option>
                            <option value="SECT" <?php echo ($edit_staff && $edit_staff['division'] === 'SECT') ? 'selected' : ''; ?>>SECT</option>
                            <option value="FWS" <?php echo ($edit_staff && $edit_staff['division'] === 'FWS') ? 'selected' : ''; ?>>FWS</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <?php if ($edit_staff): ?>
                            <button type="submit" name="update_staff" class="btn btn-success">Update Staff</button>
                            <a href="staff.php" class="btn">Cancel</a>
                        <?php else: ?>
                            <button type="submit" name="add_staff" class="btn btn-success">Add Staff</button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <?php if (isset($registration_qr)): ?>
        <div class="card no-print">
            <div class="card-header">
                <h3 class="card-title">Registration QR Code Generated</h3>
            </div>
            <div class="card-content">
                <div class="qr-display">
                    <img src="../<?php echo $registration_qr['image_path']; ?>" alt="Registration QR Code" class="qr-code-img">
                    <p><strong>Staff ID:</strong> <?php echo htmlspecialchars($staff_id); ?></p>
                    <p><strong>QR Code:</strong> <?php echo htmlspecialchars($registration_qr['code']); ?></p>
                    <p><strong>Registration URL:</strong> <a href="<?php echo $registration_qr['url']; ?>" target="_blank"><?php echo $registration_qr['url']; ?></a></p>
                    <p style="color: #7f8c8d; margin-top: 15px;">
                        Share this QR code with the staff member for device registration.
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchBox').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('staffTable');
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length - 1; j++) { // Exclude actions column
                    if (cells[j].textContent.toLowerCase().includes(searchTerm)) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        });

        // Auto-focus on search box
        document.getElementById('searchBox').focus();
    </script>
</body>
</html>
