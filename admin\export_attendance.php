<?php
// NCHM Attendance System - Excel Export
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    die('Access denied');
}

require_once '../classes/DatabaseManager.php';

try {
    $pdo = new PDO("mysql:host=localhost;dbname=attendance_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get date parameter (default to today)
    $date = $_GET['date'] ?? date('Y-m-d');
    $format = $_GET['format'] ?? 'excel';
    
    // Get attendance data for the specified date
    $stmt = $pdo->prepare("
        SELECT s.name, s.eid_cid_permit, s.position_title, s.division,
               GROUP_CONCAT(
                   CONCAT(al.action, ' at ', TIME_FORMAT(al.timestamp, '%H:%i:%s'))
                   ORDER BY al.timestamp SEPARATOR ' | '
               ) as actions,
               MIN(CASE WHEN al.action = 'clock_in' THEN TIME(al.timestamp) END) as clock_in_time,
               MAX(CASE WHEN al.action = 'clock_out' THEN TIME(al.timestamp) END) as clock_out_time,
               CASE
                   WHEN COUNT(CASE WHEN al.action = 'clock_in' THEN 1 END) > 0
                        AND COUNT(CASE WHEN al.action = 'clock_out' THEN 1 END) > 0
                   THEN 'Complete'
                   WHEN COUNT(CASE WHEN al.action = 'clock_in' THEN 1 END) > 0
                   THEN 'Present'
                   ELSE 'Absent'
               END as status,
               ? as attendance_date
        FROM staff s
        LEFT JOIN attendance_logs al ON s.id = al.staff_id AND DATE(al.timestamp) = ?
        WHERE s.is_registered = 1
        GROUP BY s.id, s.name, s.eid_cid_permit, s.position_title, s.division
        ORDER BY s.name
    ");
    $stmt->execute([$date, $date]);
    $attendance_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($format === 'excel') {
        // Set headers for Excel download
        $filename = "NCHM_Attendance_" . $date . ".xls";
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        // Start Excel output
        echo "\xEF\xBB\xBF"; // UTF-8 BOM for proper encoding
        ?>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #000; padding: 8px; text-align: left; }
                th { background-color: #f0f0f0; font-weight: bold; }
                .header { text-align: center; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>NCHM - National Center for Hydrology and Meteorology</h2>
                <h3>Daily Attendance Report</h3>
                <p>Date: <?php echo date('F j, Y', strtotime($date)); ?></p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>EID/CID/Permit</th>
                        <th>Position</th>
                        <th>Division</th>
                        <th>Clock In Time</th>
                        <th>Clock Out Time</th>
                        <th>Status</th>
                        <th>All Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attendance_data as $record): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($record['name']); ?></td>
                        <td><?php echo htmlspecialchars($record['eid_cid_permit']); ?></td>
                        <td><?php echo htmlspecialchars($record['position_title']); ?></td>
                        <td><?php echo htmlspecialchars($record['division']); ?></td>
                        <td><?php echo $record['clock_in_time'] ? htmlspecialchars($record['clock_in_time']) : '-'; ?></td>
                        <td><?php echo $record['clock_out_time'] ? htmlspecialchars($record['clock_out_time']) : '-'; ?></td>
                        <td><?php echo htmlspecialchars($record['status']); ?></td>
                        <td><?php echo $record['actions'] ? htmlspecialchars($record['actions']) : 'No actions'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div style="margin-top: 30px; font-size: 12px;">
                <p>Generated on: <?php echo date('Y-m-d H:i:s'); ?></p>
                <p>Total Records: <?php echo count($attendance_data); ?></p>
            </div>
        </body>
        </html>
        <?php
    } else {
        // JSON format for API usage
        header('Content-Type: application/json');
        echo json_encode([
            'date' => $date,
            'total_records' => count($attendance_data),
            'data' => $attendance_data
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    if ($format === 'excel') {
        echo "Error generating report: " . $e->getMessage();
    } else {
        echo json_encode(['error' => $e->getMessage()]);
    }
}
?>
