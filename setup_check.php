<?php
echo "<h1>🔍 Database Setup Check</h1>";

try {
    require_once 'config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "<p style='color: green;'>✅ <strong>Database Connected Successfully!</strong></p>";
        
        // Check what tables exist
        echo "<h2>📊 Existing Tables</h2>";
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p style='color: orange;'>⚠️ <strong>No tables found in database 'attendance_db'</strong></p>";
            echo "<p>We need to create the database tables.</p>";
        } else {
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>✅ $table</li>";
            }
            echo "</ul>";
        }
        
        // Check if database exists
        echo "<h2>🗄️ Database Info</h2>";
        $stmt = $conn->query("SELECT DATABASE() as current_db");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Current Database:</strong> " . $result['current_db'] . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ <strong>Database Connection Failed!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
    
    if (strpos($e->getMessage(), "Unknown database") !== false) {
        echo "<p style='color: orange;'>⚠️ <strong>The database 'attendance_db' doesn't exist yet.</strong></p>";
        echo "<p>We need to create it first.</p>";
    }
}

echo "<hr>";
echo "<h2>🔧 Next Steps</h2>";
echo "<p>If tables are missing, we'll need to:</p>";
echo "<ol>";
echo "<li>Create the database (if it doesn't exist)</li>";
echo "<li>Import the database schema</li>";
echo "<li>Set up initial admin user</li>";
echo "</ol>";

echo "<p><a href='setup_database.php'>🚀 Run Database Setup</a></p>";
?>
