<?php
echo "<h2>DHM Attendance System - Connection Test</h2>";

// Test 1: PHP Version
echo "<h3>1. PHP Version</h3>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Required: 7.4+ " . (version_compare(PHP_VERSION, '7.4.0') >= 0 ? "✅ OK" : "❌ FAIL") . "<br><br>";

// Test 2: Required Extensions
echo "<h3>2. PHP Extensions</h3>";
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'json'];
foreach ($required_extensions as $ext) {
    echo "$ext: " . (extension_loaded($ext) ? "✅ Loaded" : "❌ Missing") . "<br>";
}
echo "<br>";

// Test 3: Database Connection
echo "<h3>3. Database Connection</h3>";
try {
    require_once 'config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Test staff table
        $stmt = $conn->query("SELECT COUNT(*) FROM staff");
        $staff_count = $stmt->fetchColumn();
        echo "Staff records found: $staff_count<br>";
        
        // Test admin users table
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM admin_users");
            $admin_count = $stmt->fetchColumn();
            echo "Admin users found: $admin_count<br>";
        } catch (Exception $e) {
            echo "❌ Admin users table not found. Run enhanced_schema.sql<br>";
        }
        
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
echo "<br>";

// Test 4: Directory Permissions
echo "<h3>4. Directory Permissions</h3>";
$directories = ['qr_codes', 'logs'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    echo "$dir: " . (is_writable($dir) ? "✅ Writable" : "❌ Not writable") . "<br>";
}
echo "<br>";

// Test 5: File Access
echo "<h3>5. File Access Test</h3>";
$files = [
    'index.php' => 'Main page',
    'admin/login.php' => 'Admin login',
    'admin/dashboard.php' => 'Admin dashboard',
    'classes/DatabaseManager.php' => 'Database manager'
];

foreach ($files as $file => $description) {
    echo "$description ($file): " . (file_exists($file) ? "✅ Found" : "❌ Missing") . "<br>";
}
echo "<br>";

// Test 6: Server Information
echo "<h3>6. Server Information</h3>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "Server IP: " . ($_SERVER['SERVER_ADDR'] ?? 'Unknown') . "<br>";
echo "Client IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "<br>";

echo "<br><hr>";
echo "<h3>Quick Links</h3>";
echo "<a href='index.php'>Main Page</a> | ";
echo "<a href='admin/test.php'>Admin Test</a> | ";
echo "<a href='admin/login.php'>Admin Login</a>";
?>
