<?php
session_start();
require_once 'classes/QRCodeManager.php';
require_once 'classes/DatabaseManager.php';

$db = new DatabaseManager();
$qr_manager = new QRCodeManager();

// Get client IP
function getClientIP() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

$client_ip = getClientIP();
$message = '';
$error = '';
$staff_info = null;
$last_attendance = null;
$qr_code_valid = false;

// Validate IP range
if (!$db->validateIP($client_ip)) {
    $error = 'Access denied. Please connect to the office Wi-Fi network.';
}

// Check if QR code is provided and valid
$code = $_GET['code'] ?? '';
if (!empty($code) && !$error) {
    // Validate QR code
    $qr_validation = $qr_manager->validateQRCode($code);
    if ($qr_validation['valid']) {
        $qr_data = $qr_validation['data'];

        // Check if it's a daily attendance QR
        if ($qr_data['qr_type'] === 'daily_attendance') {
            $qr_code_valid = true;

            // Get device information from POST (when form is submitted) or session
            $device_token = $_POST['device_token'] ?? $_SESSION['device_token'] ?? '';
            $device_model = $_POST['device_model'] ?? $_SESSION['device_model'] ?? '';
            $registration_id = $_POST['registration_id'] ?? $_SESSION['registration_id'] ?? '';

            // If this is a POST request with device info, try to match the device
            if (!empty($device_token) && !empty($device_model) && !empty($registration_id)) {
                // Debug: Log the received values
                error_log("Attendance Debug - Received: token=$device_token, model=$device_model, reg_id=$registration_id");

                // Try to find registered device by registration ID (primary match)
                $query = "SELECT dr.*, s.name, s.eid_cid_permit, s.position_title, s.division
                          FROM device_registrations dr
                          JOIN staff s ON dr.staff_id = s.id
                          WHERE dr.registration_id = :registration_id
                          AND dr.is_active = 1 AND s.is_registered = 1";
                $stmt = $db->conn->prepare($query);
                $stmt->bindParam(':registration_id', $registration_id);
                $stmt->execute();
                $staff_info = $stmt->fetch(PDO::FETCH_ASSOC);

                // Debug: Log the query result
                error_log("Attendance Debug - Query result: " . ($staff_info ? "Found staff: " . $staff_info['name'] : "No staff found"));

                if ($staff_info) {
                    // Store device info in session for subsequent requests
                    $_SESSION['device_token'] = $device_token;
                    $_SESSION['device_model'] = $device_model;
                    $_SESSION['registration_id'] = $registration_id;

                    // Get last attendance to determine available actions
                    $last_attendance = $db->getLastAttendance($staff_info['staff_id']);
                } else {
                    $error = 'Device not registered or registration details do not match. Please register your device first.';
                }
            } else {
                // First time loading - show device detection page
                $staff_info = null;
            }
        } else {
            $error = 'Invalid attendance QR code';
        }
    } else {
        $error = $qr_validation['message'];
    }
} elseif (empty($code)) {
    // Redirect to main page if no QR code is provided
    header('Location: index.php');
    exit;
}

// Process attendance action - only if we have valid context
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error && $staff_info && $qr_code_valid) {
    $action = $_POST['action'] ?? '';

    if ($action === 'clock_in' || $action === 'clock_out') {
        // Record attendance
        if ($db->recordAttendance($staff_info['staff_id'], $action)) {
            // Update device last used
            $db->updateDeviceLastUsed($staff_info['registration_id']);

            // Increment QR usage
            $db->incrementQRUsage($code);

            // Log attendance
            $db->logAction('attendance', ucfirst(str_replace('_', ' ', $action)),
                json_encode(['staff_id' => $staff_info['staff_id'], 'action' => $action]),
                $staff_info['staff_id'], null, $client_ip, $_SERVER['HTTP_USER_AGENT']);

            $message = ucfirst(str_replace('_', ' ', $action)) . ' successful!';

            // Update last attendance for display
            $last_attendance = $db->getLastAttendance($staff_info['staff_id']);
        } else {
            $error = 'Failed to record attendance. Please try again.';
        }
    }
    // No error for invalid actions - just ignore them
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance - NCHM Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: 500;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .attendance-success {
            text-align: center;
            padding: 20px;
        }

        .success-icon {
            width: 60px;
            height: 60px;
            background: #28a745;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .clock-in {
            background: #28a745;
        }

        .clock-out {
            background: #dc3545;
        }

        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #6c757d;
        }

        .time-display {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px 0;
        }

        .date-display {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .staff-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .staff-info h2 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .staff-id {
            color: #495057;
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 8px;
        }

        .staff-position {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .staff-division {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 0;
        }

        .attendance-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn-clock-in {
            background: linear-gradient(45deg, #28a745, #20c997);
            flex: 1;
        }

        .btn-clock-out {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            flex: 1;
        }

        .btn-clock-in:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-clock-out:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }

            .attendance-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">NCHM</div>
        <h1>Attendance System</h1>
        <p class="subtitle">National Center for Hydrology and Meteorology</p>

        <div class="date-display"><?php echo date('l, F j, Y'); ?></div>
        <div class="time-display" id="currentTime"></div>

        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($message && !$error): ?>
            <div class="attendance-success">
                <div class="success-icon">✓</div>
                <div class="success"><?php echo htmlspecialchars($message); ?></div>
                <?php if ($staff_info): ?>
                    <p><strong><?php echo htmlspecialchars($staff_info['name']); ?></strong></p>
                    <p><?php echo htmlspecialchars($staff_info['eid_cid_permit']); ?></p>
                    <p><?php echo htmlspecialchars($staff_info['position_title'] ?? 'Staff'); ?> - <?php echo htmlspecialchars($staff_info['division'] ?? 'N/A'); ?></p>
                    <p style="margin-top: 20px; color: #6c757d;">
                        Have a great day!
                    </p>
                <?php endif; ?>
            </div>
        <?php elseif ($staff_info && !$error): ?>
            <div class="staff-info">
                <h2><?php echo htmlspecialchars($staff_info['name']); ?></h2>
                <p class="staff-id"><?php echo htmlspecialchars($staff_info['eid_cid_permit']); ?></p>
                <p class="staff-position"><?php echo htmlspecialchars($staff_info['position_title'] ?? 'Staff'); ?></p>
                <p class="staff-division"><?php echo htmlspecialchars($staff_info['division'] ?? 'N/A'); ?></p>
            </div>

            <div class="device-info">
                <strong>Current Device:</strong><br>
                IP: <?php echo htmlspecialchars($client_ip); ?><br>
                Time: <span id="deviceTime"></span>
            </div>

            <form method="POST" id="attendanceForm">
                <input type="hidden" name="device_token" id="deviceToken" value="">
                <input type="hidden" name="device_model" id="deviceModel" value="">
                <input type="hidden" name="registration_id" id="registrationId" value="">

                <div class="attendance-buttons">
                    <button type="submit" name="action" value="clock_in" class="btn btn-clock-in">
                        🕐 Clock In
                    </button>
                    <button type="submit" name="action" value="clock_out" class="btn btn-clock-out">
                        🕐 Clock Out
                    </button>
                </div>
            </form>
        <?php elseif ($qr_code_valid && !$error): ?>
            <!-- Device Detection Section -->
            <div class="device-detection">
                <h2>Device Detection</h2>
                <p>Please wait while we detect your device...</p>
                <div id="deviceStatus">Detecting device...</div>

                <form method="POST" id="deviceForm" style="display: none;">
                    <input type="hidden" name="device_token" id="detectedDeviceToken" value="">
                    <input type="hidden" name="device_model" id="detectedDeviceModel" value="">
                    <input type="hidden" name="registration_id" id="detectedRegistrationId" value="">
                    <button type="submit" class="btn">Continue to Attendance</button>
                </form>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
            const deviceTimeElement = document.getElementById('deviceTime');
            if (deviceTimeElement) {
                deviceTimeElement.textContent = timeString;
            }
        }

        // Update time every second
        updateTime();
        setInterval(updateTime, 1000);

        // Audio feedback function
        function playAudioFeedback(action) {
            // Create speech synthesis
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance();

                if (action === 'clock_in') {
                    utterance.text = 'Welcome';
                } else if (action === 'clock_out') {
                    utterance.text = 'Thank you';
                }

                utterance.rate = 1;
                utterance.pitch = 1;
                utterance.volume = 0.8;

                speechSynthesis.speak(utterance);
            }
        }

        // Device detection function
        function detectDevice() {
            // Check if localStorage is available (iOS Safari private mode issue)
            function isLocalStorageAvailable() {
                try {
                    const test = 'localStorage_test';
                    localStorage.setItem(test, test);
                    localStorage.removeItem(test);
                    return true;
                } catch (e) {
                    return false;
                }
            }

            if (!isLocalStorageAvailable()) {
                document.getElementById('deviceStatus').innerHTML = `
                    <div class="error">
                        ❌ localStorage is not available.<br>
                        This might be due to:<br>
                        • Private browsing mode<br>
                        • Browser security settings<br>
                        • iOS Safari restrictions<br><br>
                        Please try:<br>
                        1. Disable private browsing<br>
                        2. Clear browser cache<br>
                        3. Try a different browser<br><br>
                        <a href="index.php" style="background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Registration</a>
                    </div>
                `;
                return;
            }

            // Get device model/info
            const deviceModel = navigator.userAgent.match(/\(([^)]+)\)/)?.[1] || 'Unknown Device';

            // Try to get stored registration ID and device token from localStorage
            let registrationId = null;
            let deviceToken = null;

            try {
                registrationId = localStorage.getItem('attendance_registration_id');
                deviceToken = localStorage.getItem('attendance_device_token');
            } catch (e) {
                console.error('Error accessing localStorage:', e);
            }

            // Debug: Log the values
            console.log('Device Detection Debug:');
            console.log('Device Token from localStorage:', deviceToken);
            console.log('Registration ID from localStorage:', registrationId);
            console.log('Device Model:', deviceModel);
            console.log('All localStorage keys:', Object.keys(localStorage));
            console.log('localStorage contents:', localStorage);

            if (!registrationId || !deviceToken) {
                // If no registration ID or device token found, show error with more debugging info
                document.getElementById('deviceStatus').innerHTML = `
                    <div class="error">
                        Device not registered or registration details do not match.<br>
                        Please register your device first.<br>
                        <small>Debug: Missing - Registration ID: ${registrationId ? 'Found' : 'Missing'}, Device Token: ${deviceToken ? 'Found' : 'Missing'}</small><br>
                        <small>Available localStorage keys: ${Object.keys(localStorage).join(', ') || 'None'}</small><br>
                        <small>localStorage size: ${localStorage.length}</small><br>
                        <small>User Agent: ${navigator.userAgent.substring(0, 100)}...</small>
                        <div style="margin-top: 15px;">
                            <a href="index.php" style="background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Go to Registration</a>
                            <button onclick="testLocalStorage()" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Test localStorage</button>
                        </div>
                    </div>
                `;
                return;
            }

            // Set the detected values
            document.getElementById('detectedDeviceToken').value = deviceToken;
            document.getElementById('detectedDeviceModel').value = deviceModel;
            document.getElementById('detectedRegistrationId').value = registrationId;

            // Show success message
            document.getElementById('deviceStatus').innerHTML =
                '<div class="success">Device detected successfully! Loading attendance page...<br><small>Debug: Using registration ID: ' + registrationId + ' and device token: ' + deviceToken.substring(0, 8) + '...</small></div>';

            // Auto-submit the form after a short delay
            setTimeout(() => {
                document.getElementById('deviceForm').submit();
            }, 1500);
        }

        // Add confirmation and audio feedback for attendance actions
        document.addEventListener('DOMContentLoaded', function() {
            // Device detection on page load
            const deviceDetection = document.querySelector('.device-detection');
            if (deviceDetection) {
                setTimeout(detectDevice, 1000); // Delay for better UX
            }

            // Handle attendance form submission
            const attendanceForm = document.getElementById('attendanceForm');
            if (attendanceForm) {
                // Set device info from localStorage if available
                const registrationId = localStorage.getItem('attendance_registration_id');
                const deviceToken = localStorage.getItem('attendance_device_token');
                if (registrationId && deviceToken) {
                    const deviceModel = navigator.userAgent.match(/\(([^)]+)\)/)?.[1] || 'Unknown Device';

                    document.getElementById('deviceToken').value = deviceToken;
                    document.getElementById('deviceModel').value = deviceModel;
                    document.getElementById('registrationId').value = registrationId;
                }

                attendanceForm.addEventListener('submit', function(e) {
                    const action = e.submitter.value;
                    const actionText = action.replace('_', ' ');

                    if (confirm(`Are you sure you want to ${actionText}?`)) {
                        // Play audio feedback
                        playAudioFeedback(action);
                    } else {
                        e.preventDefault();
                    }
                });
            }

            // Play success audio if there's a success message
            <?php if ($message && !$error): ?>
                <?php
                $action = '';
                if (strpos($message, 'Clock in') !== false) {
                    $action = 'clock_in';
                } elseif (strpos($message, 'Clock out') !== false) {
                    $action = 'clock_out';
                }
                ?>
                <?php if ($action): ?>
                    setTimeout(() => playAudioFeedback('<?php echo $action; ?>'), 500);
                <?php endif; ?>
            <?php endif; ?>
        });

        // Test function to manually set localStorage for debugging
        function testLocalStorage() {
            try {
                // Test if localStorage is available and working
                const testKey = 'test_' + Date.now();
                localStorage.setItem(testKey, 'test_value');
                const testValue = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);

                if (testValue !== 'test_value') {
                    throw new Error('localStorage read/write test failed');
                }

                const testRegistrationId = 'REG_TEST_' + Date.now();
                const testDeviceToken = 'DT_TEST_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const testDeviceModel = navigator.userAgent.match(/\(([^)]+)\)/)?.[1] || 'Unknown Device';

                localStorage.setItem('attendance_registration_id', testRegistrationId);
                localStorage.setItem('attendance_device_token', testDeviceToken);
                localStorage.setItem('attendance_device_model', testDeviceModel);

                // Verify the values were stored
                const storedRegId = localStorage.getItem('attendance_registration_id');
                const storedToken = localStorage.getItem('attendance_device_token');
                const storedModel = localStorage.getItem('attendance_device_model');

                alert('✅ localStorage Test Successful!\n\n' +
                      'Registration ID: ' + storedRegId + '\n' +
                      'Device Token: ' + storedToken.substring(0, 16) + '...\n' +
                      'Device Model: ' + storedModel + '\n\n' +
                      'Please refresh the page to test attendance detection.');

            } catch (e) {
                alert('❌ localStorage Test Failed!\n\n' +
                      'Error: ' + e.message + '\n\n' +
                      'This might be due to:\n' +
                      '- Private browsing mode\n' +
                      '- Browser security settings\n' +
                      '- iOS Safari restrictions\n\n' +
                      'Please try:\n' +
                      '1. Disable private browsing\n' +
                      '2. Clear browser cache\n' +
                      '3. Try a different browser');
            }
            console.log('Test registration ID set in localStorage:', testRegistrationId);
            console.log('Test device token set in localStorage:', testDeviceToken);
        }
    </script>
</body>
</html>
