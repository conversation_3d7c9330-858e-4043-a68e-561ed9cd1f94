<?php
// Simple script to create placeholder logo images
echo "<h1>Creating Logo Placeholders</h1>";

// Create assets directory if it doesn't exist
if (!is_dir('assets')) {
    mkdir('assets', 0755, true);
    echo "<p>✅ Created assets directory</p>";
}

// Function to create a simple circular logo
function createCircularLogo($text, $filename, $bg_color = [52, 152, 219], $text_color = [255, 255, 255]) {
    $size = 200;
    $image = imagecreatetruecolor($size, $size);
    
    // Enable alpha blending
    imagealphablending($image, false);
    imagesavealpha($image, true);
    
    // Create transparent background
    $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
    imagefill($image, 0, 0, $transparent);
    
    // Create colors
    $bg = imagecolorallocate($image, $bg_color[0], $bg_color[1], $bg_color[2]);
    $text_col = imagecolorallocate($image, $text_color[0], $text_color[1], $text_color[2]);
    
    // Draw circle
    imagefilledellipse($image, $size/2, $size/2, $size-20, $size-20, $bg);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    $x = ($size - $text_width) / 2;
    $y = ($size - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $text, $text_col);
    
    // Save image
    imagepng($image, $filename);
    imagedestroy($image);
    
    return file_exists($filename);
}

// Create Bhutan emblem placeholder
if (createCircularLogo('RGoB', 'assets/bhutan-emblem.png', [183, 28, 28])) {
    echo "<p>✅ Created Bhutan emblem placeholder</p>";
} else {
    echo "<p>❌ Failed to create Bhutan emblem</p>";
}

// Create NCHM logo placeholder
if (createCircularLogo('NCHM', 'assets/nchm-logo.png', [52, 152, 219])) {
    echo "<p>✅ Created NCHM logo placeholder</p>";
} else {
    echo "<p>❌ Failed to create NCHM logo</p>";
}

echo "<hr>";
echo "<h2>🎉 Logo Creation Complete!</h2>";
echo "<p>Placeholder logos have been created. You can replace them with actual logo files later.</p>";
echo "<p><a href='index.php'>🏠 Go to Main Page</a></p>";

// Display the created images
echo "<h3>Preview:</h3>";
echo "<div style='display: flex; gap: 20px; justify-content: center; margin: 20px 0;'>";
if (file_exists('assets/bhutan-emblem.png')) {
    echo "<div style='text-align: center;'>";
    echo "<img src='assets/bhutan-emblem.png' style='width: 80px; height: 80px; border-radius: 50%;'><br>";
    echo "<small>Bhutan Emblem</small>";
    echo "</div>";
}
if (file_exists('assets/nchm-logo.png')) {
    echo "<div style='text-align: center;'>";
    echo "<img src='assets/nchm-logo.png' style='width: 80px; height: 80px; border-radius: 50%;'><br>";
    echo "<small>NCHM Logo</small>";
    echo "</div>";
}
echo "</div>";
?>
